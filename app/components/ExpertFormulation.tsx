import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  TextInput,
} from "react-native";
import {
  Zap,
  CheckCircle,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Beaker,
  Edit3,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  Shield,
} from "lucide-react-native";
import UserPreferencesService, { Brand, ProductLine } from "../services/UserPreferencesService";

interface DiagnosisData {
  zones: {
    roots: ZoneAnalysis;
    midLengths: ZoneAnalysis;
    ends: ZoneAnalysis;
  };
  overall: {
    naturalLevel: number;
    undertone: string;
    porosity: string;
    condition: string;
    canasPercentage: number;
    diameter: string;
    density: string;
    elasticity: string;
    resistance: string;
    existingReflections: string[];
  };
  analysisConfidence: number;
  timestamp: string;
}

interface ZoneAnalysis {
  naturalLevel: number;
  undertone: string;
  condition: string;
  porosity: string;
  canasPercentage: number;
  notes: string;
}

interface DesiredColorData {
  referenceImages: any[];
  verbalDescription: string;
  selectedPalette?: string;
  colorGoals: string[];
  priorityLevel: "maintenance" | "change" | "transformation";
  timeline: "immediate" | "gradual" | "flexible";
  analysisComplete: boolean;
}

interface ChemicalHistoryData {
  treatments: any[];
  allergies: string[];
  sensitivities: string[];
  lastUpdate: string;
  isComplete: boolean;
}

interface FormulaProduct {
  id: string;
  brand: string;
  productLine: string;
  shade: string;
  volume: string;
  ratio: string;
  notes?: string;
}

interface FormulationStep {
  id: string;
  order: number;
  description: string;
  products: FormulaProduct[];
  processingTime: number;
  temperature?: string;
  technique?: string;
  notes?: string;
}

interface GeneratedFormula {
  id: string;
  mainFormula: FormulaProduct[];
  steps: FormulationStep[];
  totalTime: number;
  difficulty: "easy" | "medium" | "hard";
  estimatedCost: number;
  warnings: string[];
  aftercare: string[];
  viabilityAnalysis: {
    achievable: boolean;
    sessionsRequired: number;
    riskLevel: "low" | "medium" | "high";
    recommendations: string[];
  };
  aiConfidence: number;
  timestamp: string;
}

interface ExpertFormulationProps {
  diagnosis: DiagnosisData;
  desiredColor: DesiredColorData;
  chemicalHistory: ChemicalHistoryData;
  onFormulationComplete: (formula: GeneratedFormula) => void;
  onBack?: () => void;
  clientName: string;
}

const ExpertFormulation: React.FC<ExpertFormulationProps> = ({
  diagnosis,
  desiredColor,
  chemicalHistory,
  onFormulationComplete,
  onBack,
  clientName,
}) => {
  const [availableBrands, setAvailableBrands] = useState<Brand[]>([]);
  const [availableProductLines, setAvailableProductLines] = useState<ProductLine[]>([]);
  const [stylistContext, setStylistContext] = useState<any>(null);
  const [generatedFormula, setGeneratedFormula] = useState<GeneratedFormula | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editableFormula, setEditableFormula] = useState<GeneratedFormula | null>(null);
  const [stylistNotes, setStylistNotes] = useState("");
  const [generationProgress, setGenerationProgress] = useState(0);

  useEffect(() => {
    loadUserPreferences();
  }, []);

  const loadUserPreferences = async () => {
    try {
      const preferencesService = UserPreferencesService.getInstance();
      const context = await preferencesService.getFormulationContext();
      
      setAvailableBrands(context.brands);
      setAvailableProductLines(context.productLines);
      setStylistContext(context);
    } catch (error) {
      console.error("Error loading user preferences:", error);
      Alert.alert("Error", "No se pudieron cargar las preferencias del usuario");
    }
  };

  const generateIntelligentFormula = async (): Promise<GeneratedFormula> => {
    const steps = [
      "Analizando diagnóstico capilar...",
      "Evaluando color deseado...",
      "Revisando historial químico...",
      "Seleccionando productos disponibles...",
      "Calculando proporciones óptimas...",
      "Evaluando viabilidad y riesgos...",
      "Generando plan de aplicación...",
      "Optimizando fórmula final...",
    ];

    for (let i = 0; i < steps.length; i++) {
      setGenerationProgress((i + 1) / steps.length * 100);
      await new Promise(resolve => setTimeout(resolve, 800));
    }

    // Generate realistic formula based on available products
    const selectedProducts = availableProductLines.filter(line => 
      line.category === "permanent" || line.category === "semi-permanent"
    );

    if (selectedProducts.length === 0) {
      throw new Error("No hay productos de coloración disponibles en tus marcas seleccionadas");
    }

    const mainProduct = selectedProducts[0];
    const needsLightening = diagnosis.overall.naturalLevel < 6 && 
                           desiredColor.colorGoals.includes("Aclarar color natural");

    const mainFormula: FormulaProduct[] = [
      {
        id: "main_color",
        brand: mainProduct.name.split(" ")[0],
        productLine: mainProduct.name,
        shade: needsLightening ? "7.1" : "6.3",
        volume: needsLightening ? "30vol" : "20vol",
        ratio: "1:1",
        notes: "Color base principal",
      },
    ];

    // Add lightening product if needed
    if (needsLightening) {
      const bleachProduct = availableProductLines.find(line => line.category === "bleach");
      if (bleachProduct) {
        mainFormula.unshift({
          id: "pre_lightening",
          brand: bleachProduct.name.split(" ")[0],
          productLine: bleachProduct.name,
          shade: "Decoloración",
          volume: "20vol",
          ratio: "1:2",
          notes: "Pre-aclarado necesario",
        });
      }
    }

    const steps: FormulationStep[] = [
      {
        id: "step_1",
        order: 1,
        description: needsLightening ? "Pre-aclarado" : "Preparación",
        products: needsLightening ? [mainFormula[0]] : [],
        processingTime: needsLightening ? 30 : 5,
        technique: needsLightening ? "Aplicación en secciones" : "Preparación del cabello",
        notes: needsLightening ? "Controlar el proceso cada 10 minutos" : "Champú clarificante",
      },
      {
        id: "step_2",
        order: 2,
        description: "Aplicación de color",
        products: needsLightening ? [mainFormula[1]] : [mainFormula[0]],
        processingTime: 35,
        technique: "Aplicación raíces a puntas",
        notes: "Aplicar primero en raíces, luego medios y puntas",
      },
    ];

    const warnings: string[] = [];
    if (chemicalHistory.allergies.includes("PPD")) {
      warnings.push("⚠️ Cliente alérgico a PPD - Verificar composición del producto");
    }
    if (diagnosis.overall.condition === "Severamente dañado") {
      warnings.push("⚠️ Cabello muy dañado - Considerar tratamiento reconstructor previo");
    }
    if (needsLightening && diagnosis.overall.porosity === "Alta") {
      warnings.push("⚠️ Cabello poroso + decoloración - Riesgo de sobreaclarado");
    }

    const mockFormula: GeneratedFormula = {
      id: `formula_${Date.now()}`,
      mainFormula,
      steps,
      totalTime: steps.reduce((total, step) => total + step.processingTime, 0),
      difficulty: needsLightening ? "medium" : "easy",
      estimatedCost: needsLightening ? 45 : 30,
      warnings,
      aftercare: [
        "Champú sin sulfatos para color",
        "Mascarilla hidratante 2x por semana",
        "Protector térmico antes del peinado",
        "Evitar agua muy caliente",
      ],
      viabilityAnalysis: {
        achievable: true,
        sessionsRequired: needsLightening ? 1 : 1,
        riskLevel: warnings.length > 1 ? "medium" : "low",
        recommendations: [
          "Realizar prueba de mecha previa",
          "Aplicar tratamiento acondicionador post-color",
          "Programar retoque en 6-8 semanas",
        ],
      },
      aiConfidence: 89,
      timestamp: new Date().toISOString(),
    };

    return mockFormula;
  };

  const handleGenerateFormula = async () => {
    if (availableProductLines.length === 0) {
      Alert.alert(
        "Productos No Disponibles",
        "No tienes productos de coloración seleccionados en tus preferencias. Ve a Configuración > Marcas y Productos para seleccionar tus productos disponibles."
      );
      return;
    }

    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      const formula = await generateIntelligentFormula();
      setGeneratedFormula(formula);
      setEditableFormula({ ...formula });

      if (formula.warnings.length > 0) {
        Alert.alert(
          "Advertencias Importantes",
          formula.warnings.join("\n\n"),
          [{ text: "Entendido", style: "default" }]
        );
      }
    } catch (error) {
      console.error("Error generating formula:", error);
      Alert.alert("Error", error instanceof Error ? error.message : "No se pudo generar la fórmula");
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  };

  const handleEditFormula = () => {
    setIsEditing(true);
  };

  const handleSaveEdits = () => {
    if (editableFormula) {
      setGeneratedFormula({ ...editableFormula });
      setIsEditing(false);
      Alert.alert("Guardado", "Los cambios en la fórmula han sido guardados.");
    }
  };

  const handleConfirmFormula = () => {
    if (!generatedFormula) return;

    const finalFormula = {
      ...generatedFormula,
      stylistNotes,
      timestamp: new Date().toISOString(),
    };

    onFormulationComplete(finalFormula);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "easy": return "text-green-600";
      case "medium": return "text-yellow-600";
      case "hard": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "low": return "text-green-600";
      case "medium": return "text-yellow-600";
      case "high": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  if (isGenerating) {
    return (
      <View className="flex-1 bg-gray-50 justify-center items-center">
        <Zap size={48} color="#8B5CF6" className="mb-4" />
        <Text className="text-lg font-semibold text-gray-800 mb-2">
          Generando Fórmula Experta
        </Text>
        <Text className="text-gray-600 text-center px-8 mb-4">
          Analizando diagnóstico, color deseado e historial químico para crear 
          la formulación más precisa con tus productos disponibles...
        </Text>
        <View className="w-64 bg-gray-200 rounded-full h-2 mb-2">
          <View
            className="bg-purple-500 rounded-full h-2 transition-all duration-300"
            style={{ width: `${generationProgress}%` }}
          />
        </View>
        <Text className="text-sm text-gray-500">
          {Math.round(generationProgress)}% completado
        </Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      <View className="p-4">
        <View className="flex-row items-center justify-between mb-4">
          <View>
            <Text className="text-2xl font-bold text-gray-800">
              Formulación Experta
            </Text>
            <Text className="text-gray-600">
              {clientName} - IA + Control Profesional
            </Text>
          </View>
          {stylistContext && (
            <View className="items-end">
              <Text className="text-sm font-medium text-purple-700">
                {stylistContext.stylistName}
              </Text>
              <Text className="text-xs text-gray-500">
                {availableProductLines.length} productos disponibles
              </Text>
            </View>
          )}
        </View>

        {!generatedFormula ? (
          <View className="bg-purple-50 p-4 rounded-lg mb-4">
            <View className="flex-row items-center mb-2">
              <Beaker size={20} color="#8B5CF6" />
              <Text className="ml-2 font-semibold text-purple-900">
                Formulación Inteligente
              </Text>
            </View>
            <Text className="text-purple-800 text-sm mb-3">
              La IA analizará el diagnóstico, color deseado e historial químico 
              para generar una fórmula personalizada usando tus productos disponibles.
            </Text>
            <TouchableOpacity
              className="bg-purple-500 py-3 px-4 rounded-lg flex-row items-center justify-center"
              onPress={handleGenerateFormula}
            >
              <Zap size={18} color="white" />
              <Text className="text-white font-semibold ml-2">
                Generar Fórmula con IA
              </Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View className="bg-green-50 p-4 rounded-lg mb-4">
            <View className="flex-row items-center justify-between mb-2">
              <View className="flex-row items-center">
                <CheckCircle size={20} color="#10B981" />
                <Text className="ml-2 font-semibold text-green-900">
                  Fórmula Generada
                </Text>
              </View>
              <View className="flex-row items-center">
                <Text className="text-sm text-green-700 mr-2">
                  {generatedFormula.aiConfidence}% confianza
                </Text>
                <TouchableOpacity
                  onPress={handleEditFormula}
                  className="bg-green-100 p-2 rounded-lg"
                >
                  <Edit3 size={16} color="#10B981" />
                </TouchableOpacity>
              </View>
            </View>
            <Text className="text-green-800 text-sm">
              Fórmula optimizada generada. Revisa los detalles y realiza ajustes 
              según tu criterio profesional.
            </Text>
          </View>
        )}
      </View>

      {generatedFormula && (
        <ScrollView className="flex-1 px-4">
          {/* Formula Overview */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <Text className="text-lg font-semibold mb-3">Resumen de la Fórmula</Text>
            
            <View className="flex-row justify-between mb-3">
              <View className="flex-1 mr-2">
                <Text className="text-sm text-gray-600">Dificultad</Text>
                <Text className={`font-medium ${getDifficultyColor(generatedFormula.difficulty)}`}>
                  {generatedFormula.difficulty === "easy" ? "Fácil" :
                   generatedFormula.difficulty === "medium" ? "Media" : "Difícil"}
                </Text>
              </View>
              <View className="flex-1 mr-2">
                <Text className="text-sm text-gray-600">Tiempo Total</Text>
                <Text className="font-medium">{generatedFormula.totalTime} min</Text>
              </View>
              <View className="flex-1">
                <Text className="text-sm text-gray-600">Coste Estimado</Text>
                <Text className="font-medium">€{generatedFormula.estimatedCost}</Text>
              </View>
            </View>

            <View className="border-t border-gray-200 pt-3">
              <Text className="text-sm text-gray-600 mb-1">Riesgo</Text>
              <Text className={`font-medium ${getRiskColor(generatedFormula.viabilityAnalysis.riskLevel)}`}>
                {generatedFormula.viabilityAnalysis.riskLevel === "low" ? "Bajo" :
                 generatedFormula.viabilityAnalysis.riskLevel === "medium" ? "Medio" : "Alto"}
              </Text>
            </View>
          </View>

          {/* Main Formula */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <Text className="text-lg font-semibold mb-3">Fórmula Principal</Text>
            
            {generatedFormula.mainFormula.map((product, index) => (
              <View key={product.id} className="border-b border-gray-100 pb-3 mb-3 last:border-b-0 last:mb-0">
                <View className="flex-row justify-between items-start mb-2">
                  <View className="flex-1">
                    <Text className="font-medium">{product.productLine}</Text>
                    <Text className="text-sm text-gray-600">{product.brand}</Text>
                  </View>
                  <View className="items-end">
                    <Text className="font-medium">{product.shade}</Text>
                    <Text className="text-sm text-gray-600">{product.volume}</Text>
                  </View>
                </View>
                <View className="flex-row justify-between">
                  <Text className="text-sm text-gray-600">Proporción: {product.ratio}</Text>
                  {product.notes && (
                    <Text className="text-sm text-blue-600">{product.notes}</Text>
                  )}
                </View>
              </View>
            ))}
          </View>

          {/* Application Steps */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <Text className="text-lg font-semibold mb-3">Pasos de Aplicación</Text>
            
            {generatedFormula.steps.map((step, index) => (
              <View key={step.id} className="border-l-4 border-purple-400 pl-4 mb-4 last:mb-0">
                <View className="flex-row items-center mb-2">
                  <View className="bg-purple-500 rounded-full w-6 h-6 items-center justify-center mr-3">
                    <Text className="text-white text-sm font-bold">{step.order}</Text>
                  </View>
                  <Text className="font-semibold flex-1">{step.description}</Text>
                  <View className="flex-row items-center">
                    <Clock size={14} color="#6B7280" />
                    <Text className="text-sm text-gray-600 ml-1">{step.processingTime} min</Text>
                  </View>
                </View>
                
                {step.technique && (
                  <Text className="text-sm text-gray-700 mb-2">
                    <Text className="font-medium">Técnica:</Text> {step.technique}
                  </Text>
                )}
                
                {step.notes && (
                  <Text className="text-sm text-blue-700 mb-2">
                    <Text className="font-medium">Notas:</Text> {step.notes}
                  </Text>
                )}
                
                {step.products.length > 0 && (
                  <View className="bg-gray-50 rounded-lg p-2">
                    {step.products.map((product, pIndex) => (
                      <Text key={pIndex} className="text-sm text-gray-700">
                        • {product.productLine} {product.shade} ({product.ratio})
                      </Text>
                    ))}
                  </View>
                )}
              </View>
            ))}
          </View>

          {/* Warnings */}
          {generatedFormula.warnings.length > 0 && (
            <View className="bg-red-50 rounded-lg p-4 mb-4">
              <View className="flex-row items-center mb-2">
                <AlertTriangle size={20} color="#EF4444" />
                <Text className="ml-2 font-semibold text-red-900">
                  Advertencias Importantes
                </Text>
              </View>
              {generatedFormula.warnings.map((warning, index) => (
                <Text key={index} className="text-red-800 text-sm mb-1">
                  {warning}
                </Text>
              ))}
            </View>
          )}

          {/* Viability Analysis */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <Text className="text-lg font-semibold mb-3">Análisis de Viabilidad</Text>
            
            <View className="space-y-2">
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Alcanzable:</Text>
                <Text className={`font-medium ${
                  generatedFormula.viabilityAnalysis.achievable ? "text-green-600" : "text-red-600"
                }`}>
                  {generatedFormula.viabilityAnalysis.achievable ? "Sí" : "No"}
                </Text>
              </View>
              
              <View className="flex-row justify-between">
                <Text className="text-gray-600">Sesiones requeridas:</Text>
                <Text className="font-medium">
                  {generatedFormula.viabilityAnalysis.sessionsRequired}
                </Text>
              </View>
            </View>

            {generatedFormula.viabilityAnalysis.recommendations.length > 0 && (
              <View className="mt-3 pt-3 border-t border-gray-200">
                <Text className="font-medium text-gray-800 mb-2">Recomendaciones:</Text>
                {generatedFormula.viabilityAnalysis.recommendations.map((rec, index) => (
                  <Text key={index} className="text-sm text-gray-700 mb-1">
                    • {rec}
                  </Text>
                ))}
              </View>
            )}
          </View>

          {/* Aftercare */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <Text className="text-lg font-semibold mb-3">Cuidados Post-Color</Text>
            {generatedFormula.aftercare.map((care, index) => (
              <Text key={index} className="text-gray-700 text-sm mb-1">
                • {care}
              </Text>
            ))}
          </View>

          {/* Stylist Notes */}
          <View className="bg-white rounded-lg p-4 mb-6">
            <Text className="text-lg font-semibold mb-3">Notas del Estilista</Text>
            <TextInput
              className="border border-gray-300 rounded-lg p-3"
              placeholder="Añade tus observaciones, modificaciones o consideraciones especiales sobre esta fórmula..."
              value={stylistNotes}
              onChangeText={setStylistNotes}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
        </ScrollView>
      )}

      {/* Action Buttons */}
      {generatedFormula && (
        <View className="p-4 bg-white border-t border-gray-200">
          <View className="flex-row space-x-3">
            <TouchableOpacity
              className="flex-1 bg-gray-200 py-3 px-4 rounded-lg flex-row items-center justify-center"
              onPress={handleGenerateFormula}
            >
              <RefreshCw size={18} color="#6B7280" />
              <Text className="text-gray-700 font-semibold ml-2">
                Regenerar
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              className="flex-1 bg-purple-500 py-3 px-4 rounded-lg flex-row items-center justify-center"
              onPress={handleConfirmFormula}
            >
              <Save size={18} color="white" />
              <Text className="text-white font-semibold ml-2">
                Confirmar Fórmula
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}
    </View>
  );
};

export default ExpertFormulation;
