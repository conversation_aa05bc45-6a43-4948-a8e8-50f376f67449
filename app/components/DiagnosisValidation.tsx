import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
} from "react-native";
import {
  Check<PERSON>ir<PERSON>,
  Edit3,
  Eye,
  AlertTriangle,
  Save,
  RefreshCw,
  Zap,
  User,
} from "lucide-react-native";

interface ZoneAnalysis {
  naturalLevel: number;
  undertone: string;
  condition: string;
  porosity: string;
  canasPercentage: number;
  notes: string;
}

interface DiagnosisData {
  zones: {
    roots: ZoneAnalysis;
    midLengths: ZoneAnalysis;
    ends: ZoneAnalysis;
  };
  overall: {
    naturalLevel: number;
    undertone: string;
    porosity: string;
    condition: string;
    canasPercentage: number;
    diameter: string;
    density: string;
    elasticity: string;
    resistance: string;
    existingReflections: string[];
  };
  analysisConfidence: number;
  timestamp: string;
}

interface DiagnosisValidationProps {
  aiDiagnosis: DiagnosisData;
  onValidationComplete: (validatedDiagnosis: DiagnosisData, stylistNotes: string) => void;
  onBack?: () => void;
  clientName: string;
  stylistName: string;
}

const DiagnosisValidation: React.FC<DiagnosisValidationProps> = ({
  aiDiagnosis,
  onValidationComplete,
  onBack,
  clientName,
  stylistName,
}) => {
  const [diagnosis, setDiagnosis] = useState<DiagnosisData>(aiDiagnosis);
  const [editingZone, setEditingZone] = useState<string | null>(null);
  const [editingOverall, setEditingOverall] = useState(false);
  const [stylistNotes, setStylistNotes] = useState("");
  const [validationStatus, setValidationStatus] = useState({
    roots: false,
    midLengths: false,
    ends: false,
    overall: false,
  });

  const naturalLevels = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
  const undertones = ["Cálido", "Frío", "Neutro"];
  const conditions = ["Excelente", "Saludable", "Ligeramente dañado", "Dañado", "Severamente dañado"];
  const porosities = ["Baja", "Media", "Media-Alta", "Alta"];
  const diameters = ["Fino", "Medio", "Grueso"];
  const densities = ["Baja", "Media", "Alta"];
  const elasticities = ["Excelente", "Buena", "Reducida", "Pobre"];
  const resistances = ["Alta", "Media", "Baja"];

  const updateZoneAnalysis = (zone: keyof typeof diagnosis.zones, field: string, value: any) => {
    setDiagnosis(prev => ({
      ...prev,
      zones: {
        ...prev.zones,
        [zone]: {
          ...prev.zones[zone],
          [field]: value,
        },
      },
    }));
  };

  const updateOverallAnalysis = (field: string, value: any) => {
    setDiagnosis(prev => ({
      ...prev,
      overall: {
        ...prev.overall,
        [field]: value,
      },
    }));
  };

  const validateZone = (zone: keyof typeof diagnosis.zones) => {
    setValidationStatus(prev => ({ ...prev, [zone]: true }));
    setEditingZone(null);
  };

  const validateOverall = () => {
    setValidationStatus(prev => ({ ...prev, overall: true }));
    setEditingOverall(false);
  };

  const isFullyValidated = () => {
    return Object.values(validationStatus).every(status => status);
  };

  const handleComplete = () => {
    if (!isFullyValidated()) {
      Alert.alert(
        "Validación Incompleta",
        "Por favor, valida todas las secciones del diagnóstico antes de continuar."
      );
      return;
    }

    if (!stylistNotes.trim()) {
      Alert.alert(
        "Notas del Estilista",
        "¿Deseas añadir notas profesionales sobre este diagnóstico?",
        [
          { text: "Continuar sin notas", onPress: () => completeValidation() },
          { text: "Añadir notas", style: "cancel" },
        ]
      );
      return;
    }

    completeValidation();
  };

  const completeValidation = () => {
    const validatedDiagnosis = {
      ...diagnosis,
      timestamp: new Date().toISOString(),
    };

    onValidationComplete(validatedDiagnosis, stylistNotes);
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 85) return "text-green-600";
    if (confidence >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  const getConfidenceText = (confidence: number) => {
    if (confidence >= 85) return "Alta Confianza";
    if (confidence >= 70) return "Confianza Media";
    return "Baja Confianza";
  };

  const renderZoneEditor = (zone: keyof typeof diagnosis.zones, zoneName: string) => {
    const zoneData = diagnosis.zones[zone];
    const isEditing = editingZone === zone;
    const isValidated = validationStatus[zone];

    return (
      <View
        key={zone}
        className={`bg-white rounded-lg p-4 mb-4 border-2 ${
          isValidated ? "border-green-200" : isEditing ? "border-blue-200" : "border-gray-200"
        }`}
      >
        <View className="flex-row items-center justify-between mb-3">
          <View className="flex-row items-center">
            <Text className="text-lg font-semibold">{zoneName}</Text>
            {isValidated && (
              <CheckCircle size={20} color="#10B981" className="ml-2" />
            )}
          </View>
          <View className="flex-row items-center">
            <TouchableOpacity
              onPress={() => setEditingZone(isEditing ? null : zone)}
              className={`p-2 rounded-lg mr-2 ${
                isEditing ? "bg-blue-100" : "bg-gray-100"
              }`}
            >
              <Edit3 size={16} color={isEditing ? "#3B82F6" : "#6B7280"} />
            </TouchableOpacity>
            {!isValidated && (
              <TouchableOpacity
                onPress={() => validateZone(zone)}
                className="bg-green-500 px-3 py-1 rounded-lg"
              >
                <Text className="text-white text-sm">Validar</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {isEditing ? (
          <View className="space-y-3">
            <View>
              <Text className="text-sm font-medium text-gray-700 mb-1">
                Nivel Natural
              </Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View className="flex-row">
                  {naturalLevels.map(level => (
                    <TouchableOpacity
                      key={level}
                      onPress={() => updateZoneAnalysis(zone, "naturalLevel", level)}
                      className={`px-3 py-2 rounded-lg mr-2 ${
                        zoneData.naturalLevel === level ? "bg-blue-500" : "bg-gray-200"
                      }`}
                    >
                      <Text className={`text-sm ${
                        zoneData.naturalLevel === level ? "text-white" : "text-gray-700"
                      }`}>
                        {level}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>

            <View>
              <Text className="text-sm font-medium text-gray-700 mb-1">
                Subtono
              </Text>
              <View className="flex-row">
                {undertones.map(undertone => (
                  <TouchableOpacity
                    key={undertone}
                    onPress={() => updateZoneAnalysis(zone, "undertone", undertone)}
                    className={`px-3 py-2 rounded-lg mr-2 ${
                      zoneData.undertone === undertone ? "bg-blue-500" : "bg-gray-200"
                    }`}
                  >
                    <Text className={`text-sm ${
                      zoneData.undertone === undertone ? "text-white" : "text-gray-700"
                    }`}>
                      {undertone}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View>
              <Text className="text-sm font-medium text-gray-700 mb-1">
                Condición
              </Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <View className="flex-row">
                  {conditions.map(condition => (
                    <TouchableOpacity
                      key={condition}
                      onPress={() => updateZoneAnalysis(zone, "condition", condition)}
                      className={`px-3 py-2 rounded-lg mr-2 ${
                        zoneData.condition === condition ? "bg-blue-500" : "bg-gray-200"
                      }`}
                    >
                      <Text className={`text-sm ${
                        zoneData.condition === condition ? "text-white" : "text-gray-700"
                      }`}>
                        {condition}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
            </View>

            <View>
              <Text className="text-sm font-medium text-gray-700 mb-1">
                Porosidad
              </Text>
              <View className="flex-row">
                {porosities.map(porosity => (
                  <TouchableOpacity
                    key={porosity}
                    onPress={() => updateZoneAnalysis(zone, "porosity", porosity)}
                    className={`px-3 py-2 rounded-lg mr-2 ${
                      zoneData.porosity === porosity ? "bg-blue-500" : "bg-gray-200"
                    }`}
                  >
                    <Text className={`text-sm ${
                      zoneData.porosity === porosity ? "text-white" : "text-gray-700"
                    }`}>
                      {porosity}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            <View>
              <Text className="text-sm font-medium text-gray-700 mb-1">
                Porcentaje de Canas
              </Text>
              <TextInput
                className="border border-gray-300 rounded-lg p-3"
                placeholder="0-100"
                value={zoneData.canasPercentage.toString()}
                onChangeText={(text) => {
                  const value = parseInt(text) || 0;
                  updateZoneAnalysis(zone, "canasPercentage", Math.min(100, Math.max(0, value)));
                }}
                keyboardType="numeric"
              />
            </View>

            <View>
              <Text className="text-sm font-medium text-gray-700 mb-1">
                Notas de la Zona
              </Text>
              <TextInput
                className="border border-gray-300 rounded-lg p-3"
                placeholder="Observaciones específicas de esta zona..."
                value={zoneData.notes}
                onChangeText={(text) => updateZoneAnalysis(zone, "notes", text)}
                multiline
                numberOfLines={3}
              />
            </View>
          </View>
        ) : (
          <View className="space-y-2">
            <Text className="text-gray-700">
              <Text className="font-medium">Nivel Natural:</Text> {zoneData.naturalLevel}
            </Text>
            <Text className="text-gray-700">
              <Text className="font-medium">Subtono:</Text> {zoneData.undertone}
            </Text>
            <Text className="text-gray-700">
              <Text className="font-medium">Condición:</Text> {zoneData.condition}
            </Text>
            <Text className="text-gray-700">
              <Text className="font-medium">Porosidad:</Text> {zoneData.porosity}
            </Text>
            <Text className="text-gray-700">
              <Text className="font-medium">Canas:</Text> {zoneData.canasPercentage}%
            </Text>
            {zoneData.notes && (
              <Text className="text-gray-700">
                <Text className="font-medium">Notas:</Text> {zoneData.notes}
              </Text>
            )}
          </View>
        )}
      </View>
    );
  };

  return (
    <View className="flex-1 bg-gray-50">
      <View className="p-4">
        <View className="flex-row items-center justify-between mb-4">
          <View>
            <Text className="text-2xl font-bold text-gray-800">
              Validación del Diagnóstico
            </Text>
            <Text className="text-gray-600">
              {clientName} - Revisión profesional
            </Text>
          </View>
          <View className="items-end">
            <View className="flex-row items-center mb-1">
              <Zap size={16} color="#3B82F6" />
              <Text className={`ml-1 text-sm font-medium ${getConfidenceColor(aiDiagnosis.analysisConfidence)}`}>
                {getConfidenceText(aiDiagnosis.analysisConfidence)}
              </Text>
            </View>
            <Text className="text-xs text-gray-500">
              {aiDiagnosis.analysisConfidence}% confianza IA
            </Text>
          </View>
        </View>

        <View className="bg-blue-50 p-4 rounded-lg mb-4">
          <View className="flex-row items-center mb-2">
            <User size={20} color="#3B82F6" />
            <Text className="ml-2 font-semibold text-blue-900">
              Control Profesional
            </Text>
          </View>
          <Text className="text-blue-800 text-sm">
            Revisa y valida cada sección del análisis IA. Puedes editar cualquier 
            valor según tu criterio profesional. Tu validación es definitiva.
          </Text>
        </View>
      </View>

      <ScrollView className="flex-1 px-4">
        {/* Zone Analysis */}
        <Text className="text-lg font-semibold text-gray-800 mb-3">
          Análisis por Zonas
        </Text>
        
        {renderZoneEditor("roots", "Raíces")}
        {renderZoneEditor("midLengths", "Medios")}
        {renderZoneEditor("ends", "Puntas")}

        {/* Overall Analysis */}
        <Text className="text-lg font-semibold text-gray-800 mb-3 mt-6">
          Análisis General
        </Text>
        
        <View
          className={`bg-white rounded-lg p-4 mb-4 border-2 ${
            validationStatus.overall ? "border-green-200" : editingOverall ? "border-blue-200" : "border-gray-200"
          }`}
        >
          <View className="flex-row items-center justify-between mb-3">
            <View className="flex-row items-center">
              <Text className="text-lg font-semibold">Diagnóstico General</Text>
              {validationStatus.overall && (
                <CheckCircle size={20} color="#10B981" className="ml-2" />
              )}
            </View>
            <View className="flex-row items-center">
              <TouchableOpacity
                onPress={() => setEditingOverall(!editingOverall)}
                className={`p-2 rounded-lg mr-2 ${
                  editingOverall ? "bg-blue-100" : "bg-gray-100"
                }`}
              >
                <Edit3 size={16} color={editingOverall ? "#3B82F6" : "#6B7280"} />
              </TouchableOpacity>
              {!validationStatus.overall && (
                <TouchableOpacity
                  onPress={validateOverall}
                  className="bg-green-500 px-3 py-1 rounded-lg"
                >
                  <Text className="text-white text-sm">Validar</Text>
                </TouchableOpacity>
              )}
            </View>
          </View>

          {editingOverall ? (
            <View className="space-y-3">
              {/* Overall editing fields would go here - similar to zone editing */}
              <Text className="text-sm text-gray-600">
                Edición del análisis general disponible...
              </Text>
            </View>
          ) : (
            <View className="space-y-2">
              <Text className="text-gray-700">
                <Text className="font-medium">Nivel Natural Promedio:</Text> {diagnosis.overall.naturalLevel}
              </Text>
              <Text className="text-gray-700">
                <Text className="font-medium">Subtono Dominante:</Text> {diagnosis.overall.undertone}
              </Text>
              <Text className="text-gray-700">
                <Text className="font-medium">Condición General:</Text> {diagnosis.overall.condition}
              </Text>
              <Text className="text-gray-700">
                <Text className="font-medium">Porosidad:</Text> {diagnosis.overall.porosity}
              </Text>
              <Text className="text-gray-700">
                <Text className="font-medium">Canas Totales:</Text> {diagnosis.overall.canasPercentage}%
              </Text>
            </View>
          )}
        </View>

        {/* Stylist Notes */}
        <Text className="text-lg font-semibold text-gray-800 mb-3">
          Notas del Estilista
        </Text>
        
        <View className="bg-white rounded-lg p-4 mb-6">
          <TextInput
            className="border border-gray-300 rounded-lg p-3"
            placeholder="Añade tus observaciones profesionales, consideraciones especiales, o modificaciones realizadas al diagnóstico IA..."
            value={stylistNotes}
            onChangeText={setStylistNotes}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>
      </ScrollView>

      {/* Complete Button */}
      <View className="p-4 bg-white border-t border-gray-200">
        <TouchableOpacity
          className={`py-3 px-4 rounded-lg flex-row items-center justify-center ${
            isFullyValidated() ? "bg-green-500" : "bg-gray-300"
          }`}
          onPress={handleComplete}
          disabled={!isFullyValidated()}
        >
          <Save size={18} color="white" />
          <Text className="text-white font-semibold ml-2">
            {isFullyValidated() 
              ? "Confirmar Diagnóstico Validado" 
              : `Validar ${Object.values(validationStatus).filter(v => !v).length} secciones restantes`
            }
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default DiagnosisValidation;
