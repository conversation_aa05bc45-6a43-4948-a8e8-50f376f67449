import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  TextInput,
} from "react-native";
import {
  Upload,
  Camera,
  Palette,
  Eye,
  CheckCircle,
  X,
  Trash2,
  Edit3,
  Zap,
} from "lucide-react-native";
import * as ImagePicker from "expo-image-picker";
// import * as FaceDetector from "expo-face-detector";
// import * as ImageManipulator from "expo-image-manipulator";

interface ColorAnalysis {
  dominantTone: string;
  undertone: string;
  saturation: string;
  brightness: string;
  reflections: string[];
  complexity: "simple" | "medium" | "complex";
  achievability: "easy" | "moderate" | "challenging";
  estimatedSessions: number;
}

interface ReferenceImage {
  id: string;
  uri: string;
  analysis?: ColorAnalysis;
  quality: "low" | "medium" | "high";
  notes?: string;
}

interface DesiredColorData {
  referenceImages: ReferenceImage[];
  verbalDescription: string;
  selectedPalette?: string;
  colorGoals: string[];
  priorityLevel: "maintenance" | "change" | "transformation";
  timeline: "immediate" | "gradual" | "flexible";
  analysisComplete: boolean;
}

interface DesiredColorDefinitionProps {
  onColorDefinitionComplete: (colorData: DesiredColorData) => void;
  onBack?: () => void;
  clientName: string;
}

const COLOR_PALETTES = [
  { id: "warm_browns", name: "Marrones Cálidos", colors: ["#8B4513", "#A0522D", "#CD853F", "#DEB887"] },
  { id: "cool_blondes", name: "Rubios Fríos", colors: ["#F5F5DC", "#F0E68C", "#E6E6FA", "#D3D3D3"] },
  { id: "warm_blondes", name: "Rubios Cálidos", colors: ["#FFD700", "#F0E68C", "#DEB887", "#D2B48C"] },
  { id: "reds", name: "Rojos", colors: ["#8B0000", "#DC143C", "#B22222", "#CD5C5C"] },
  { id: "auburns", name: "Cobrizos", colors: ["#A52A2A", "#CD853F", "#D2691E", "#FF8C00"] },
  { id: "fantasy", name: "Fantasía", colors: ["#9370DB", "#FF1493", "#00CED1", "#32CD32"] },
];

const COLOR_GOALS = [
  "Cubrir canas",
  "Aclarar color natural",
  "Oscurecer color",
  "Cambiar subtono",
  "Añadir reflejos",
  "Color uniforme",
  "Mechas/highlights",
  "Balayage",
  "Ombré",
  "Color fantasía",
];

const DesiredColorDefinition: React.FC<DesiredColorDefinitionProps> = ({
  onColorDefinitionComplete,
  onBack,
  clientName,
}) => {
  const [referenceImages, setReferenceImages] = useState<ReferenceImage[]>([]);
  const [verbalDescription, setVerbalDescription] = useState("");
  const [selectedPalette, setSelectedPalette] = useState<string>("");
  const [selectedGoals, setSelectedGoals] = useState<string[]>([]);
  const [priorityLevel, setPriorityLevel] = useState<"maintenance" | "change" | "transformation">("change");
  const [timeline, setTimeline] = useState<"immediate" | "gradual" | "flexible">("immediate");
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  const detectAndBlurFaces = async (imageUri: string): Promise<string> => {
    try {
      // Simplified version - just return original image for now
      // TODO: Implement face detection when expo-face-detector is properly configured
      console.log("Face detection temporarily disabled - returning original image");
      return imageUri;
    } catch (error) {
      console.error("Error detecting/blurring faces:", error);
      return imageUri;
    }
  };

  const validateImageQuality = (imageUri: string): "low" | "medium" | "high" => {
    // Simplified quality assessment
    const randomQuality = Math.random();
    if (randomQuality > 0.7) return "high";
    if (randomQuality > 0.4) return "medium";
    return "low";
  };

  const analyzeColorFromImage = async (imageUri: string): Promise<ColorAnalysis> => {
    // Simulate AI color analysis
    await new Promise(resolve => setTimeout(resolve, 1500));

    const mockAnalysis: ColorAnalysis = {
      dominantTone: "Rubio dorado",
      undertone: "Cálido",
      saturation: "Media",
      brightness: "Alta",
      reflections: ["Dorados", "Miel", "Caramelo"],
      complexity: "medium",
      achievability: "moderate",
      estimatedSessions: 2,
    };

    return mockAnalysis;
  };

  const captureReferenceImage = async () => {
    if (referenceImages.length >= 2) {
      Alert.alert("Límite Alcanzado", "Máximo 2 imágenes de referencia permitidas para optimizar el análisis.");
      return;
    }

    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await processReferenceImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error("Error capturing image:", error);
      Alert.alert("Error", "No se pudo capturar la imagen.");
    }
  };

  const selectFromGallery = async () => {
    if (referenceImages.length >= 2) {
      Alert.alert("Límite Alcanzado", "Máximo 2 imágenes de referencia permitidas.");
      return;
    }

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        await processReferenceImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error("Error selecting image:", error);
      Alert.alert("Error", "No se pudo seleccionar la imagen.");
    }
  };

  const processReferenceImage = async (imageUri: string) => {
    setIsAnalyzing(true);

    try {
      // Apply face blurring
      const processedUri = await detectAndBlurFaces(imageUri);
      
      // Validate quality
      const quality = validateImageQuality(processedUri);
      
      // Analyze color
      const analysis = await analyzeColorFromImage(processedUri);

      const newImage: ReferenceImage = {
        id: `ref_${Date.now()}`,
        uri: processedUri,
        analysis,
        quality,
      };

      setReferenceImages(prev => [...prev, newImage]);

      if (quality === "low") {
        Alert.alert(
          "Calidad de Imagen",
          "La imagen tiene calidad baja. Considera tomar otra foto con mejor iluminación para un análisis más preciso."
        );
      }
    } catch (error) {
      console.error("Error processing image:", error);
      Alert.alert("Error", "No se pudo procesar la imagen.");
    } finally {
      setIsAnalyzing(false);
    }
  };

  const removeReferenceImage = (imageId: string) => {
    setReferenceImages(prev => prev.filter(img => img.id !== imageId));
  };

  const toggleColorGoal = (goal: string) => {
    setSelectedGoals(prev => 
      prev.includes(goal) 
        ? prev.filter(g => g !== goal)
        : [...prev, goal]
    );
  };

  const canProceed = () => {
    return (
      (referenceImages.length > 0 || verbalDescription.trim().length > 0) &&
      selectedGoals.length > 0
    );
  };

  const handleComplete = () => {
    if (!canProceed()) {
      Alert.alert(
        "Información Incompleta",
        "Por favor, añade al menos una imagen de referencia o descripción verbal, y selecciona los objetivos de color."
      );
      return;
    }

    const colorData: DesiredColorData = {
      referenceImages,
      verbalDescription,
      selectedPalette,
      colorGoals: selectedGoals,
      priorityLevel,
      timeline,
      analysisComplete: true,
    };

    onColorDefinitionComplete(colorData);
  };

  const showImageOptions = () => {
    Alert.alert(
      "Añadir Imagen de Referencia",
      "Elige cómo quieres añadir la imagen de referencia del color deseado",
      [
        { text: "Tomar Foto", onPress: captureReferenceImage },
        { text: "Elegir de Galería", onPress: selectFromGallery },
        { text: "Cancelar", style: "cancel" },
      ]
    );
  };

  return (
    <View className="flex-1 bg-gray-50">
      <View className="p-4">
        <View className="flex-row items-center justify-between mb-4">
          {onBack && (
            <TouchableOpacity onPress={onBack}>
              <X size={24} color="#666" />
            </TouchableOpacity>
          )}
          <View className="flex-1">
            <Text className="text-2xl font-bold text-gray-800">
              Color Deseado
            </Text>
            <Text className="text-gray-600">
              {clientName} - Define el objetivo de color
            </Text>
          </View>
        </View>

        <View className="bg-purple-50 p-4 rounded-lg mb-4">
          <View className="flex-row items-center mb-2">
            <Palette size={20} color="#8B5CF6" />
            <Text className="ml-2 font-semibold text-purple-900">
              Definición Precisa
            </Text>
          </View>
          <Text className="text-purple-800 text-sm">
            Combina imágenes de referencia, paletas digitales y descripción verbal 
            para crear la formulación más precisa.
          </Text>
        </View>
      </View>

      <ScrollView className="flex-1 px-4">
        {/* Reference Images Section */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-800 mb-3">
            Imágenes de Referencia ({referenceImages.length}/2)
          </Text>
          
          <View className="flex-row flex-wrap">
            {referenceImages.map((image) => (
              <View key={image.id} className="w-1/2 p-1">
                <View className="bg-white rounded-lg overflow-hidden">
                  <Image
                    source={{ uri: image.uri }}
                    className="w-full h-32"
                    resizeMode="cover"
                  />
                  <View className="p-2">
                    <View className="flex-row items-center justify-between mb-1">
                      <Text className={`text-xs font-medium ${
                        image.quality === "high" ? "text-green-600" :
                        image.quality === "medium" ? "text-yellow-600" : "text-red-600"
                      }`}>
                        {image.quality === "high" ? "Alta Calidad" :
                         image.quality === "medium" ? "Calidad Media" : "Baja Calidad"}
                      </Text>
                      <TouchableOpacity
                        onPress={() => removeReferenceImage(image.id)}
                        className="p-1"
                      >
                        <Trash2 size={14} color="#EF4444" />
                      </TouchableOpacity>
                    </View>
                    
                    {image.analysis && (
                      <View>
                        <Text className="text-xs text-gray-600 mb-1">
                          <Text className="font-medium">Tono:</Text> {image.analysis.dominantTone}
                        </Text>
                        <Text className="text-xs text-gray-600 mb-1">
                          <Text className="font-medium">Subtono:</Text> {image.analysis.undertone}
                        </Text>
                        <Text className="text-xs text-gray-600">
                          <Text className="font-medium">Sesiones:</Text> {image.analysis.estimatedSessions}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              </View>
            ))}
            
            {referenceImages.length < 2 && (
              <View className="w-1/2 p-1">
                <TouchableOpacity
                  onPress={showImageOptions}
                  className="bg-gray-200 rounded-lg h-32 justify-center items-center border-2 border-dashed border-gray-400"
                  disabled={isAnalyzing}
                >
                  {isAnalyzing ? (
                    <>
                      <Zap size={24} color="#6B7280" className="mb-2" />
                      <Text className="text-gray-600 text-sm">Analizando...</Text>
                    </>
                  ) : (
                    <>
                      <Upload size={24} color="#6B7280" className="mb-2" />
                      <Text className="text-gray-600 text-sm text-center">
                        Añadir{"\n"}Referencia
                      </Text>
                    </>
                  )}
                </TouchableOpacity>
              </View>
            )}
          </View>
        </View>

        {/* Color Palettes */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-800 mb-3">
            Paletas de Color
          </Text>
          
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View className="flex-row">
              {COLOR_PALETTES.map((palette) => (
                <TouchableOpacity
                  key={palette.id}
                  onPress={() => setSelectedPalette(
                    selectedPalette === palette.id ? "" : palette.id
                  )}
                  className={`mr-3 p-3 rounded-lg border-2 ${
                    selectedPalette === palette.id ? "border-purple-500 bg-purple-50" : "border-gray-200 bg-white"
                  }`}
                >
                  <Text className="text-sm font-medium mb-2">{palette.name}</Text>
                  <View className="flex-row">
                    {palette.colors.map((color, index) => (
                      <View
                        key={index}
                        className="w-6 h-6 rounded mr-1"
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
        </View>

        {/* Verbal Description */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-800 mb-3">
            Descripción Verbal
          </Text>
          
          <View className="bg-white rounded-lg p-4">
            <TextInput
              className="text-gray-800"
              placeholder="Describe detalladamente el color deseado: tono, técnica, intensidad, inspiración, etc."
              value={verbalDescription}
              onChangeText={setVerbalDescription}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
        </View>

        {/* Color Goals */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-800 mb-3">
            Objetivos de Color
          </Text>
          
          <View className="flex-row flex-wrap">
            {COLOR_GOALS.map((goal) => (
              <TouchableOpacity
                key={goal}
                onPress={() => toggleColorGoal(goal)}
                className={`mr-2 mb-2 px-3 py-2 rounded-full border ${
                  selectedGoals.includes(goal)
                    ? "border-purple-500 bg-purple-100"
                    : "border-gray-300 bg-white"
                }`}
              >
                <Text className={`text-sm ${
                  selectedGoals.includes(goal) ? "text-purple-700" : "text-gray-700"
                }`}>
                  {goal}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Priority and Timeline */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-800 mb-3">
            Prioridad y Cronograma
          </Text>
          
          <View className="bg-white rounded-lg p-4">
            <Text className="text-sm font-medium text-gray-700 mb-2">
              Nivel de Cambio
            </Text>
            <View className="flex-row mb-4">
              {[
                { key: "maintenance", label: "Mantenimiento" },
                { key: "change", label: "Cambio" },
                { key: "transformation", label: "Transformación" },
              ].map((option) => (
                <TouchableOpacity
                  key={option.key}
                  onPress={() => setPriorityLevel(option.key as any)}
                  className={`mr-3 px-3 py-2 rounded-lg ${
                    priorityLevel === option.key ? "bg-purple-500" : "bg-gray-200"
                  }`}
                >
                  <Text className={`text-sm ${
                    priorityLevel === option.key ? "text-white" : "text-gray-700"
                  }`}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text className="text-sm font-medium text-gray-700 mb-2">
              Cronograma
            </Text>
            <View className="flex-row">
              {[
                { key: "immediate", label: "Inmediato" },
                { key: "gradual", label: "Gradual" },
                { key: "flexible", label: "Flexible" },
              ].map((option) => (
                <TouchableOpacity
                  key={option.key}
                  onPress={() => setTimeline(option.key as any)}
                  className={`mr-3 px-3 py-2 rounded-lg ${
                    timeline === option.key ? "bg-purple-500" : "bg-gray-200"
                  }`}
                >
                  <Text className={`text-sm ${
                    timeline === option.key ? "text-white" : "text-gray-700"
                  }`}>
                    {option.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Complete Button */}
      <View className="p-4 bg-white border-t border-gray-200">
        <TouchableOpacity
          className={`py-3 px-4 rounded-lg flex-row items-center justify-center ${
            canProceed() ? "bg-purple-500" : "bg-gray-300"
          }`}
          onPress={handleComplete}
          disabled={!canProceed()}
        >
          <CheckCircle size={18} color="white" />
          <Text className="text-white font-semibold ml-2">
            {canProceed() 
              ? "Confirmar Color Deseado" 
              : "Añade referencias y objetivos"
            }
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default DesiredColorDefinition;
