import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Alert,
  BackHandler,
} from "react-native";
import {
  ArrowLeft,
  CheckCircle,
  Clock,
  User,
  Eye,
  Palette,
  Beaker,
  FileText,
  Shield,
} from "lucide-react-native";
import { useRouter } from "expo-router";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Import phase components
import ClientConsentPhase from "./phases/ClientConsentPhase";
import CurrentColorAnalysisPhase from "./phases/CurrentColorAnalysisPhase";
import DesiredColorAnalysisPhase from "./phases/DesiredColorAnalysisPhase";
import IntelligentFormulationPhase from "./phases/IntelligentFormulationPhase";
import ResultDocumentationPhase from "./phases/ResultDocumentationPhase";

interface ConsultationState {
  currentPhase: number;
  client: any;
  consentData: any;
  currentColorAnalysis: any;
  desiredColorAnalysis: any;
  formulationData: any;
  documentationData: any;
  startTime: string;
  consultationId: string;
  stylistBrands: string[];
}

interface UltraIntelligentColorConsultationProps {
  onComplete?: (consultationData: any) => void;
  onCancel?: () => void;
  initialClient?: any;
}

const PHASES = [
  { 
    id: 1, 
    name: "Cliente & Consentimiento", 
    icon: Shield, 
    description: "Selección y firma digital",
    color: "#10B981"
  },
  { 
    id: 2, 
    name: "Color Actual", 
    icon: Eye, 
    description: "Análisis IA + Validación",
    color: "#3B82F6"
  },
  { 
    id: 3, 
    name: "Color Deseado", 
    icon: Palette, 
    description: "Objetivo + Validación",
    color: "#8B5CF6"
  },
  { 
    id: 4, 
    name: "Formulación IA", 
    icon: Beaker, 
    description: "Fórmula inteligente",
    color: "#F59E0B"
  },
  { 
    id: 5, 
    name: "Documentación", 
    icon: FileText, 
    description: "Resultado + Seguimiento",
    color: "#EF4444"
  },
];

const UltraIntelligentColorConsultation: React.FC<UltraIntelligentColorConsultationProps> = ({
  onComplete = () => {},
  onCancel = () => {},
  initialClient,
}) => {
  const router = useRouter();
  const [consultationState, setConsultationState] = useState<ConsultationState>({
    currentPhase: 1,
    client: initialClient || null,
    consentData: null,
    currentColorAnalysis: null,
    desiredColorAnalysis: null,
    formulationData: null,
    documentationData: null,
    startTime: new Date().toISOString(),
    consultationId: `ultra_consultation_${Date.now()}`,
    stylistBrands: [],
  });

  useEffect(() => {
    loadConsultationState();
    loadStylistBrands();

    const backHandler = BackHandler.addEventListener("hardwareBackPress", handleBackPress);
    return () => backHandler.remove();
  }, []);

  useEffect(() => {
    saveConsultationState();
  }, [consultationState]);

  const loadStylistBrands = async () => {
    try {
      // Load stylist's preferred brands from settings
      const settings = await AsyncStorage.getItem("salonier_settings");
      if (settings) {
        const parsedSettings = JSON.parse(settings);
        const brands = parsedSettings.brands?.selectedBrands || ["loreal", "wella", "schwarzkopf"];
        setConsultationState(prev => ({ ...prev, stylistBrands: brands }));
      }
    } catch (error) {
      console.error("Error loading stylist brands:", error);
      // Default brands if settings not found
      setConsultationState(prev => ({ 
        ...prev, 
        stylistBrands: ["loreal", "wella", "schwarzkopf"] 
      }));
    }
  };

  const loadConsultationState = async () => {
    try {
      const savedState = await AsyncStorage.getItem(`consultation_${consultationState.consultationId}`);
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        setConsultationState(prev => ({ ...prev, ...parsedState }));
      }
    } catch (error) {
      console.error("Error loading consultation state:", error);
    }
  };

  const saveConsultationState = async () => {
    try {
      await AsyncStorage.setItem(
        `consultation_${consultationState.consultationId}`,
        JSON.stringify(consultationState)
      );
    } catch (error) {
      console.error("Error saving consultation state:", error);
    }
  };

  const handleBackPress = (): boolean => {
    if (consultationState.currentPhase > 1) {
      handlePreviousPhase();
      return true;
    }
    return false;
  };

  const handleNextPhase = () => {
    if (consultationState.currentPhase < PHASES.length) {
      setConsultationState(prev => ({
        ...prev,
        currentPhase: prev.currentPhase + 1,
      }));
    }
  };

  const handlePreviousPhase = () => {
    if (consultationState.currentPhase > 1) {
      setConsultationState(prev => ({
        ...prev,
        currentPhase: prev.currentPhase - 1,
      }));
    } else {
      handleCancel();
    }
  };

  const handleCancel = () => {
    Alert.alert(
      "Cancelar Consulta Ultra-Inteligente",
      "¿Estás seguro de que quieres cancelar? Se perderá todo el progreso.",
      [
        { text: "Continuar", style: "cancel" },
        {
          text: "Cancelar Consulta",
          style: "destructive",
          onPress: () => {
            AsyncStorage.removeItem(`consultation_${consultationState.consultationId}`);
            router.back();
          },
        },
      ]
    );
  };

  // Phase handlers
  const handleClientConsentComplete = (client: any, consent: any) => {
    setConsultationState(prev => ({
      ...prev,
      client,
      consentData: consent,
    }));
    handleNextPhase();
  };

  const handleCurrentColorAnalysisComplete = (analysis: any) => {
    setConsultationState(prev => ({
      ...prev,
      currentColorAnalysis: analysis,
    }));
    handleNextPhase();
  };

  const handleDesiredColorAnalysisComplete = (analysis: any) => {
    setConsultationState(prev => ({
      ...prev,
      desiredColorAnalysis: analysis,
    }));
    handleNextPhase();
  };

  const handleFormulationComplete = (formulation: any) => {
    setConsultationState(prev => ({
      ...prev,
      formulationData: formulation,
    }));
    handleNextPhase();
  };

  const handleDocumentationComplete = (documentation: any) => {
    setConsultationState(prev => ({
      ...prev,
      documentationData: documentation,
    }));
    completeConsultation();
  };

  const completeConsultation = async () => {
    const finalConsultationData = {
      ...consultationState,
      endTime: new Date().toISOString(),
      totalDuration: Date.now() - new Date(consultationState.startTime).getTime(),
      status: "completed",
      type: "ultra_intelligent_color_consultation",
    };

    try {
      await saveToClientHistory(finalConsultationData);
      await AsyncStorage.removeItem(`consultation_${consultationState.consultationId}`);
      
      onComplete(finalConsultationData);
      
      Alert.alert(
        "🎉 Consulta Ultra-Inteligente Completada",
        "La consulta de color ha sido guardada con análisis IA completo y validación profesional.",
        [
          {
            text: "Ver Historial",
            onPress: () => router.push("/components/ClientManagement"),
          },
          {
            text: "Nueva Consulta",
            onPress: () => router.push("/"),
          },
        ]
      );
    } catch (error) {
      console.error("Error completing consultation:", error);
      Alert.alert("Error", "No se pudo guardar la consulta completa.");
    }
  };

  const saveToClientHistory = async (consultationData: any) => {
    try {
      const existingHistory = await AsyncStorage.getItem("ultra_consultations");
      const history = existingHistory ? JSON.parse(existingHistory) : [];
      
      history.push(consultationData);
      
      await AsyncStorage.setItem("ultra_consultations", JSON.stringify(history));
    } catch (error) {
      console.error("Error saving to client history:", error);
      throw error;
    }
  };

  const renderPhaseIndicator = () => {
    return (
      <View className="bg-white p-4 border-b border-gray-200">
        <View className="flex-row items-center justify-between mb-3">
          <TouchableOpacity onPress={handlePreviousPhase}>
            <ArrowLeft size={24} color="#374151" />
          </TouchableOpacity>
          <View className="items-center">
            <Text className="text-lg font-bold text-gray-800">
              Consulta Ultra-Inteligente
            </Text>
            <Text className="text-sm text-gray-600">
              Fase {consultationState.currentPhase} de {PHASES.length}
            </Text>
          </View>
          <View className="flex-row items-center">
            <Clock size={16} color="#6B7280" />
            <Text className="text-sm text-gray-600 ml-1">
              {Math.round((Date.now() - new Date(consultationState.startTime).getTime()) / 60000)} min
            </Text>
          </View>
        </View>

        <View className="flex-row justify-between">
          {PHASES.map((phase, index) => {
            const isActive = consultationState.currentPhase === phase.id;
            const isCompleted = consultationState.currentPhase > phase.id;
            const IconComponent = phase.icon;

            return (
              <View key={phase.id} className="flex-1 items-center mx-1">
                <View 
                  className={`w-10 h-10 rounded-full items-center justify-center mb-2`}
                  style={{ 
                    backgroundColor: isCompleted ? "#10B981" : isActive ? phase.color : "#D1D5DB" 
                  }}
                >
                  {isCompleted ? (
                    <CheckCircle size={18} color="white" />
                  ) : (
                    <IconComponent size={18} color="white" />
                  )}
                </View>
                <Text className={`text-xs text-center font-medium ${
                  isActive ? "text-gray-800" : "text-gray-600"
                }`}>
                  {phase.name}
                </Text>
                <Text className="text-xs text-center text-gray-500">
                  {phase.description}
                </Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  const renderCurrentPhase = () => {
    const { currentPhase, client, currentColorAnalysis, desiredColorAnalysis, formulationData, stylistBrands } = consultationState;

    switch (currentPhase) {
      case 1:
        return (
          <ClientConsentPhase
            onComplete={handleClientConsentComplete}
            onBack={handleCancel}
            initialClient={client}
          />
        );

      case 2:
        return (
          <CurrentColorAnalysisPhase
            client={client}
            onComplete={handleCurrentColorAnalysisComplete}
            onBack={handlePreviousPhase}
          />
        );

      case 3:
        return (
          <DesiredColorAnalysisPhase
            client={client}
            currentColorAnalysis={currentColorAnalysis}
            onComplete={handleDesiredColorAnalysisComplete}
            onBack={handlePreviousPhase}
          />
        );

      case 4:
        return (
          <IntelligentFormulationPhase
            client={client}
            currentColorAnalysis={currentColorAnalysis}
            desiredColorAnalysis={desiredColorAnalysis}
            stylistBrands={stylistBrands}
            onComplete={handleFormulationComplete}
            onBack={handlePreviousPhase}
          />
        );

      case 5:
        return (
          <ResultDocumentationPhase
            client={client}
            formulationData={formulationData}
            onComplete={handleDocumentationComplete}
            onBack={handlePreviousPhase}
          />
        );

      default:
        return (
          <View className="flex-1 justify-center items-center">
            <Text className="text-lg text-gray-600">Fase no encontrada</Text>
          </View>
        );
    }
  };

  return (
    <View className="flex-1 bg-gray-50">
      {renderPhaseIndicator()}
      {renderCurrentPhase()}
    </View>
  );
};

export default UltraIntelligentColorConsultation;
