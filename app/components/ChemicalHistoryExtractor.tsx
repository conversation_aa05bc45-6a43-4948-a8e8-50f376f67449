import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
  Switch,
} from "react-native";
import {
  History,
  Plus,
  Edit3,
  Trash2,
  Al<PERSON><PERSON>riangle,
  CheckCircle,
  Calendar,
  Zap,
} from "lucide-react-native";

interface ChemicalTreatment {
  id: string;
  date: string;
  type: string;
  brand?: string;
  product?: string;
  formula?: string;
  result: string;
  notes?: string;
  stylist?: string;
  isAutoExtracted: boolean;
}

interface ChemicalHistoryData {
  treatments: ChemicalTreatment[];
  allergies: string[];
  sensitivities: string[];
  lastUpdate: string;
  isComplete: boolean;
}

interface ChemicalHistoryExtractorProps {
  clientId: string;
  clientName: string;
  onHistoryComplete: (history: ChemicalHistoryData) => void;
  onBack?: () => void;
}

const ChemicalHistoryExtractor: React.FC<ChemicalHistoryExtractorProps> = ({
  clientId,
  clientName,
  onHistoryComplete,
  onBack,
}) => {
  const [treatments, setTreatments] = useState<ChemicalTreatment[]>([]);
  const [allergies, setAllergies] = useState<string[]>([]);
  const [sensitivities, setSensitivities] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showAddTreatment, setShowAddTreatment] = useState(false);
  const [newAllergy, setNewAllergy] = useState("");
  const [newSensitivity, setNewSensitivity] = useState("");
  
  // New treatment form
  const [newTreatment, setNewTreatment] = useState({
    date: "",
    type: "",
    brand: "",
    product: "",
    formula: "",
    result: "",
    notes: "",
  });

  useEffect(() => {
    extractHistoryFromServices();
  }, [clientId]);

  const extractHistoryFromServices = async () => {
    setIsLoading(true);
    
    try {
      // Simulate extraction from client service history
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // Mock extracted treatments from service history
      const extractedTreatments: ChemicalTreatment[] = [
        {
          id: "auto_1",
          date: "2024-01-15",
          type: "Coloración",
          brand: "L'Oréal Professional",
          product: "Majirel",
          formula: "7.1 + 20vol (1:1)",
          result: "Rubio ceniza medio",
          notes: "Cliente satisfecha con el resultado",
          stylist: "Ana García",
          isAutoExtracted: true,
        },
        {
          id: "auto_2",
          date: "2023-11-20",
          type: "Mechas",
          brand: "Wella",
          product: "Blondor",
          formula: "Blondor + 30vol (1:2)",
          result: "Mechas rubias",
          notes: "Proceso de 45 minutos",
          stylist: "Carlos Ruiz",
          isAutoExtracted: true,
        },
        {
          id: "auto_3",
          date: "2023-08-10",
          type: "Alisado",
          brand: "Keratin Complex",
          product: "Natural Keratin Smoothing Treatment",
          result: "Cabello liso y manejable",
          notes: "Duración estimada 3-4 meses",
          stylist: "María López",
          isAutoExtracted: true,
        },
      ];

      // Mock extracted allergies/sensitivities
      const extractedAllergies = ["PPD", "Amoníaco"];
      const extractedSensitivities = ["Cuero cabelludo sensible"];

      setTreatments(extractedTreatments);
      setAllergies(extractedAllergies);
      setSensitivities(extractedSensitivities);
      
    } catch (error) {
      console.error("Error extracting history:", error);
      Alert.alert("Error", "No se pudo extraer el historial automáticamente");
    } finally {
      setIsLoading(false);
    }
  };

  const addManualTreatment = () => {
    if (!newTreatment.date || !newTreatment.type) {
      Alert.alert("Error", "La fecha y el tipo de tratamiento son obligatorios");
      return;
    }

    const treatment: ChemicalTreatment = {
      id: `manual_${Date.now()}`,
      ...newTreatment,
      isAutoExtracted: false,
    };

    setTreatments(prev => [treatment, ...prev]);
    setNewTreatment({
      date: "",
      type: "",
      brand: "",
      product: "",
      formula: "",
      result: "",
      notes: "",
    });
    setShowAddTreatment(false);
  };

  const removeTreatment = (treatmentId: string) => {
    Alert.alert(
      "Eliminar Tratamiento",
      "¿Estás seguro de que quieres eliminar este tratamiento del historial?",
      [
        { text: "Cancelar", style: "cancel" },
        {
          text: "Eliminar",
          style: "destructive",
          onPress: () => {
            setTreatments(prev => prev.filter(t => t.id !== treatmentId));
          },
        },
      ]
    );
  };

  const addAllergy = () => {
    if (newAllergy.trim() && !allergies.includes(newAllergy.trim())) {
      setAllergies(prev => [...prev, newAllergy.trim()]);
      setNewAllergy("");
    }
  };

  const removeAllergy = (allergy: string) => {
    setAllergies(prev => prev.filter(a => a !== allergy));
  };

  const addSensitivity = () => {
    if (newSensitivity.trim() && !sensitivities.includes(newSensitivity.trim())) {
      setSensitivities(prev => [...prev, newSensitivity.trim()]);
      setNewSensitivity("");
    }
  };

  const removeSensitivity = (sensitivity: string) => {
    setSensitivities(prev => prev.filter(s => s !== sensitivity));
  };

  const handleComplete = () => {
    const historyData: ChemicalHistoryData = {
      treatments,
      allergies,
      sensitivities,
      lastUpdate: new Date().toISOString(),
      isComplete: true,
    };

    onHistoryComplete(historyData);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("es-ES", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getTreatmentIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case "coloración":
      case "color":
        return "🎨";
      case "mechas":
      case "highlights":
        return "✨";
      case "alisado":
      case "straightening":
        return "📏";
      case "permanente":
      case "perm":
        return "🌀";
      case "tratamiento":
      case "treatment":
        return "💆";
      default:
        return "💇";
    }
  };

  if (isLoading) {
    return (
      <View className="flex-1 bg-gray-50 justify-center items-center">
        <Zap size={48} color="#3B82F6" className="mb-4" />
        <Text className="text-lg font-semibold text-gray-800 mb-2">
          Extrayendo Historial Químico
        </Text>
        <Text className="text-gray-600 text-center px-8">
          Analizando el historial de servicios de {clientName} para extraer 
          tratamientos químicos previos...
        </Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      <View className="p-4">
        <View className="flex-row items-center justify-between mb-4">
          <View>
            <Text className="text-2xl font-bold text-gray-800">
              Historial Químico
            </Text>
            <Text className="text-gray-600">
              {clientName} - {treatments.length} tratamientos encontrados
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => setShowAddTreatment(true)}
            className="bg-blue-500 p-3 rounded-lg"
          >
            <Plus size={20} color="white" />
          </TouchableOpacity>
        </View>

        {/* Auto-extracted info */}
        <View className="bg-green-50 p-4 rounded-lg mb-4">
          <View className="flex-row items-center mb-2">
            <CheckCircle size={20} color="#10B981" />
            <Text className="ml-2 font-semibold text-green-900">
              Extracción Automática Completada
            </Text>
          </View>
          <Text className="text-green-800 text-sm">
            Se han extraído {treatments.filter(t => t.isAutoExtracted).length} tratamientos 
            del historial de servicios. Puedes revisar, editar o añadir información adicional.
          </Text>
        </View>
      </View>

      <ScrollView className="flex-1 px-4">
        {/* Treatments Section */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-800 mb-3">
            Tratamientos Químicos
          </Text>
          
          {treatments.map((treatment) => (
            <View
              key={treatment.id}
              className={`bg-white rounded-lg p-4 mb-3 border-l-4 ${
                treatment.isAutoExtracted ? "border-green-400" : "border-blue-400"
              }`}
            >
              <View className="flex-row items-center justify-between mb-2">
                <View className="flex-row items-center">
                  <Text className="text-2xl mr-2">
                    {getTreatmentIcon(treatment.type)}
                  </Text>
                  <View>
                    <Text className="font-semibold text-gray-800">
                      {treatment.type}
                    </Text>
                    <Text className="text-sm text-gray-600">
                      {formatDate(treatment.date)}
                    </Text>
                  </View>
                </View>
                <View className="flex-row items-center">
                  {treatment.isAutoExtracted && (
                    <View className="bg-green-100 px-2 py-1 rounded mr-2">
                      <Text className="text-xs text-green-700">Auto</Text>
                    </View>
                  )}
                  <TouchableOpacity
                    onPress={() => removeTreatment(treatment.id)}
                    className="p-1"
                  >
                    <Trash2 size={16} color="#EF4444" />
                  </TouchableOpacity>
                </View>
              </View>

              {treatment.brand && (
                <Text className="text-sm text-gray-600 mb-1">
                  <Text className="font-medium">Marca:</Text> {treatment.brand}
                </Text>
              )}
              
              {treatment.product && (
                <Text className="text-sm text-gray-600 mb-1">
                  <Text className="font-medium">Producto:</Text> {treatment.product}
                </Text>
              )}
              
              {treatment.formula && (
                <Text className="text-sm text-gray-600 mb-1">
                  <Text className="font-medium">Fórmula:</Text> {treatment.formula}
                </Text>
              )}
              
              <Text className="text-sm text-gray-600 mb-1">
                <Text className="font-medium">Resultado:</Text> {treatment.result}
              </Text>
              
              {treatment.notes && (
                <Text className="text-sm text-gray-600 mb-1">
                  <Text className="font-medium">Notas:</Text> {treatment.notes}
                </Text>
              )}
              
              {treatment.stylist && (
                <Text className="text-xs text-gray-500">
                  Estilista: {treatment.stylist}
                </Text>
              )}
            </View>
          ))}
        </View>

        {/* Allergies Section */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-800 mb-3">
            Alergias Conocidas
          </Text>
          
          <View className="bg-white rounded-lg p-4">
            <View className="flex-row flex-wrap mb-3">
              {allergies.map((allergy) => (
                <View
                  key={allergy}
                  className="bg-red-100 rounded-full px-3 py-1 mr-2 mb-2 flex-row items-center"
                >
                  <Text className="text-red-800 text-sm">{allergy}</Text>
                  <TouchableOpacity
                    onPress={() => removeAllergy(allergy)}
                    className="ml-2"
                  >
                    <Text className="text-red-600 font-bold">×</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
            
            <View className="flex-row">
              <TextInput
                className="flex-1 border border-gray-300 rounded-lg px-3 py-2 mr-2"
                placeholder="Añadir alergia..."
                value={newAllergy}
                onChangeText={setNewAllergy}
                onSubmitEditing={addAllergy}
              />
              <TouchableOpacity
                onPress={addAllergy}
                className="bg-red-500 px-4 py-2 rounded-lg"
              >
                <Plus size={16} color="white" />
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Sensitivities Section */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-800 mb-3">
            Sensibilidades
          </Text>
          
          <View className="bg-white rounded-lg p-4">
            <View className="flex-row flex-wrap mb-3">
              {sensitivities.map((sensitivity) => (
                <View
                  key={sensitivity}
                  className="bg-yellow-100 rounded-full px-3 py-1 mr-2 mb-2 flex-row items-center"
                >
                  <Text className="text-yellow-800 text-sm">{sensitivity}</Text>
                  <TouchableOpacity
                    onPress={() => removeSensitivity(sensitivity)}
                    className="ml-2"
                  >
                    <Text className="text-yellow-600 font-bold">×</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
            
            <View className="flex-row">
              <TextInput
                className="flex-1 border border-gray-300 rounded-lg px-3 py-2 mr-2"
                placeholder="Añadir sensibilidad..."
                value={newSensitivity}
                onChangeText={setNewSensitivity}
                onSubmitEditing={addSensitivity}
              />
              <TouchableOpacity
                onPress={addSensitivity}
                className="bg-yellow-500 px-4 py-2 rounded-lg"
              >
                <Plus size={16} color="white" />
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Complete Button */}
      <View className="p-4 bg-white border-t border-gray-200">
        <TouchableOpacity
          className="bg-green-500 py-3 px-4 rounded-lg"
          onPress={handleComplete}
        >
          <Text className="text-white font-semibold text-center">
            Confirmar Historial Químico
          </Text>
        </TouchableOpacity>
      </View>

      {/* Add Treatment Modal */}
      {showAddTreatment && (
        <View className="absolute inset-0 bg-black bg-opacity-50 justify-center items-center">
          <View className="bg-white rounded-lg p-6 mx-4 w-full max-w-md">
            <Text className="text-lg font-semibold mb-4">Añadir Tratamiento</Text>

            <View className="space-y-3">
              <View>
                <Text className="text-sm font-medium text-gray-700 mb-1">
                  Fecha *
                </Text>
                <TextInput
                  className="border border-gray-300 rounded-lg p-3"
                  placeholder="YYYY-MM-DD"
                  value={newTreatment.date}
                  onChangeText={(text) => setNewTreatment(prev => ({ ...prev, date: text }))}
                />
              </View>

              <View>
                <Text className="text-sm font-medium text-gray-700 mb-1">
                  Tipo de Tratamiento *
                </Text>
                <TextInput
                  className="border border-gray-300 rounded-lg p-3"
                  placeholder="Ej: Coloración, Mechas, Alisado"
                  value={newTreatment.type}
                  onChangeText={(text) => setNewTreatment(prev => ({ ...prev, type: text }))}
                />
              </View>

              <View>
                <Text className="text-sm font-medium text-gray-700 mb-1">
                  Marca
                </Text>
                <TextInput
                  className="border border-gray-300 rounded-lg p-3"
                  placeholder="Ej: L'Oréal, Wella"
                  value={newTreatment.brand}
                  onChangeText={(text) => setNewTreatment(prev => ({ ...prev, brand: text }))}
                />
              </View>

              <View>
                <Text className="text-sm font-medium text-gray-700 mb-1">
                  Producto
                </Text>
                <TextInput
                  className="border border-gray-300 rounded-lg p-3"
                  placeholder="Nombre del producto"
                  value={newTreatment.product}
                  onChangeText={(text) => setNewTreatment(prev => ({ ...prev, product: text }))}
                />
              </View>

              <View>
                <Text className="text-sm font-medium text-gray-700 mb-1">
                  Resultado
                </Text>
                <TextInput
                  className="border border-gray-300 rounded-lg p-3"
                  placeholder="Describe el resultado obtenido"
                  value={newTreatment.result}
                  onChangeText={(text) => setNewTreatment(prev => ({ ...prev, result: text }))}
                />
              </View>
            </View>

            <View className="flex-row justify-end mt-6 space-x-3">
              <TouchableOpacity
                className="bg-gray-200 px-4 py-2 rounded-lg mr-3"
                onPress={() => setShowAddTreatment(false)}
              >
                <Text className="text-gray-700">Cancelar</Text>
              </TouchableOpacity>
              <TouchableOpacity
                className="bg-blue-500 px-4 py-2 rounded-lg"
                onPress={addManualTreatment}
              >
                <Text className="text-white">Añadir</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      )}
    </View>
  );
};

export default ChemicalHistoryExtractor;
