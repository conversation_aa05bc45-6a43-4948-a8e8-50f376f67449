import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Alert,
} from "react-native";
import {
  Search,
  UserPlus,
  Shield,
  CheckCircle,
  Edit3,
  Mail,
  Phone,
  Calendar,
  FileText,
} from "lucide-react-native";
import ClientSearchBar from "../ClientSearchBar";

interface Client {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  avatar: string;
  lastService: string;
  lastVisit: string;
  isFrequent: boolean;
  totalVisits: number;
  preferredServices: string[];
  notes?: string;
}

interface ConsentData {
  photoUsage: boolean;
  aiAnalysis: boolean;
  dataStorage: boolean;
  marketing: boolean;
  signature: string;
  timestamp: string;
  deviceInfo: string;
  ipAddress: string;
  clientName: string;
  clientId: string;
  stylistWitness: string;
  revocable: boolean;
}

interface ClientConsentPhaseProps {
  onComplete: (client: Client, consent: ConsentData) => void;
  onBack?: () => void;
  initialClient?: Client;
}

const ClientConsentPhase: React.FC<ClientConsentPhaseProps> = ({
  onComplete,
  onBack,
  initialClient,
}) => {
  const [selectedClient, setSelectedClient] = useState<Client | null>(initialClient || null);
  const [showGuestForm, setShowGuestForm] = useState(false);
  const [guestName, setGuestName] = useState("");
  const [guestPhone, setGuestPhone] = useState("");
  const [guestEmail, setGuestEmail] = useState("");
  
  // Consent states (GDPR compliant)
  const [consent, setConsent] = useState({
    photoUsage: false,
    aiAnalysis: false,
    dataStorage: false,
    marketing: false,
  });
  
  // Digital signature state
  const [signatureConfirmed, setSignatureConfirmed] = useState(false);
  const [signatureTimestamp, setSignatureTimestamp] = useState<string>("");

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client);
  };

  const handleCreateGuest = (name: string) => {
    setGuestName(name);
    setShowGuestForm(true);
  };

  const createGuestClient = () => {
    if (!guestName.trim()) {
      Alert.alert("Error", "El nombre del cliente es obligatorio");
      return;
    }

    const guestClient: Client = {
      id: `guest_${Date.now()}`,
      name: guestName.trim(),
      phone: guestPhone.trim() || undefined,
      email: guestEmail.trim() || undefined,
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${guestName}`,
      lastService: "Primera visita",
      lastVisit: "Hoy",
      isFrequent: false,
      totalVisits: 0,
      preferredServices: [],
      notes: "Cliente invitado - Primera consulta ultra-inteligente",
    };

    setSelectedClient(guestClient);
    setShowGuestForm(false);
  };

  const handleConsentChange = (key: keyof typeof consent) => {
    setConsent(prev => ({ ...prev, [key]: !prev[key] }));
  };

  const getRequiredConsents = () => {
    return consent.photoUsage && consent.aiAnalysis;
  };

  const handleDigitalSignature = () => {
    const timestamp = new Date().toISOString();
    setSignatureConfirmed(true);
    setSignatureTimestamp(timestamp);
    
    Alert.alert(
      "Firma Digital Registrada",
      `Confirmación registrada el ${new Date(timestamp).toLocaleString()}\n\nDispositivo: ${getDeviceInfo()}`,
      [{ text: "Continuar", style: "default" }]
    );
  };

  const getDeviceInfo = () => {
    // In a real app, you'd get actual device info
    return "iPad Pro - iOS 17.2";
  };

  const getIPAddress = () => {
    // In a real app, you'd get actual IP
    return "*************";
  };

  const handleConfirmConsent = () => {
    if (!selectedClient) {
      Alert.alert("Error", "Selecciona un cliente primero");
      return;
    }

    if (!getRequiredConsents()) {
      Alert.alert(
        "Consentimientos Obligatorios",
        "Los consentimientos para uso de fotos y análisis IA son obligatorios para la consulta."
      );
      return;
    }

    if (!signatureConfirmed) {
      Alert.alert("Firma Requerida", "La confirmación digital del cliente es obligatoria.");
      return;
    }

    const consentData: ConsentData = {
      ...consent,
      signature: `digital_signature_${signatureTimestamp}`,
      timestamp: signatureTimestamp,
      deviceInfo: getDeviceInfo(),
      ipAddress: getIPAddress(),
      clientName: selectedClient.name,
      clientId: selectedClient.id,
      stylistWitness: "Estilista Profesional", // In real app, get from user session
      revocable: true,
    };

    // Simulate email confirmation
    if (selectedClient.email) {
      Alert.alert(
        "Email de Confirmación Enviado",
        `Se ha enviado una copia del consentimiento a ${selectedClient.email}`,
        [{ text: "Continuar", onPress: () => onComplete(selectedClient, consentData) }]
      );
    } else {
      onComplete(selectedClient, consentData);
    }
  };

  const clearSignature = () => {
    setSignatureConfirmed(false);
    setSignatureTimestamp("");
  };

  if (showGuestForm) {
    return (
      <View className="flex-1 bg-gray-50 p-4">
        <View className="mb-6">
          <Text className="text-2xl font-bold text-gray-800 mb-2">
            Cliente Invitado
          </Text>
          <Text className="text-gray-600">
            Para consultas rápidas sin crear perfil completo
          </Text>
        </View>

        <View className="bg-white rounded-lg p-4 mb-6">
          <View className="space-y-4">
            <View>
              <Text className="text-sm font-medium text-gray-700 mb-2">
                Nombre Completo *
              </Text>
              <TextInput
                className="border border-gray-300 rounded-lg p-3 bg-white"
                placeholder="Ej: María García"
                value={guestName}
                onChangeText={setGuestName}
                autoCapitalize="words"
              />
            </View>

            <View>
              <Text className="text-sm font-medium text-gray-700 mb-2">
                Teléfono (Opcional)
              </Text>
              <TextInput
                className="border border-gray-300 rounded-lg p-3 bg-white"
                placeholder="Ej: +34 666 123 456"
                value={guestPhone}
                onChangeText={setGuestPhone}
                keyboardType="phone-pad"
              />
            </View>

            <View>
              <Text className="text-sm font-medium text-gray-700 mb-2">
                Email (Opcional)
              </Text>
              <TextInput
                className="border border-gray-300 rounded-lg p-3 bg-white"
                placeholder="Ej: <EMAIL>"
                value={guestEmail}
                onChangeText={setGuestEmail}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>
          </View>
        </View>

        <View className="flex-row space-x-3">
          <TouchableOpacity
            className="flex-1 bg-gray-200 py-3 px-4 rounded-lg"
            onPress={() => setShowGuestForm(false)}
          >
            <Text className="text-gray-700 font-semibold text-center">Cancelar</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            className={`flex-1 py-3 px-4 rounded-lg ${
              guestName.trim() ? "bg-blue-500" : "bg-gray-300"
            }`}
            onPress={createGuestClient}
            disabled={!guestName.trim()}
          >
            <Text className="text-white font-semibold text-center">
              Crear Cliente
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      {!selectedClient ? (
        <View className="p-4">
          <View className="mb-6">
            <Text className="text-2xl font-bold text-gray-800 mb-2">
              Seleccionar Cliente
            </Text>
            <Text className="text-gray-600">
              Busca un cliente existente o crea uno nuevo
            </Text>
          </View>

          <ClientSearchBar
            onSelect={handleClientSelect}
            onCreateNew={handleCreateGuest}
            placeholder="Buscar por nombre, teléfono o email..."
            showFrequentFirst={true}
            maxResults={8}
          />

          <View className="mt-6 p-4 bg-blue-50 rounded-lg">
            <View className="flex-row items-center mb-2">
              <Shield size={20} color="#3B82F6" />
              <Text className="ml-2 font-semibold text-blue-900">
                Privacidad y Consentimiento
              </Text>
            </View>
            <Text className="text-sm text-blue-800">
              Cumplimos con GDPR/LOPD. El cliente firmará digitalmente 
              el consentimiento para uso de fotos y análisis IA.
            </Text>
          </View>
        </View>
      ) : (
        <ScrollView className="flex-1 p-4">
          {/* Client Info */}
          <View className="bg-white rounded-lg p-4 mb-6">
            <Text className="text-lg font-semibold mb-3">Cliente Seleccionado</Text>
            <View className="flex-row items-center">
              <View className="w-12 h-12 rounded-full bg-blue-100 items-center justify-center mr-3">
                <Text className="text-blue-600 font-bold">
                  {selectedClient.name.charAt(0)}
                </Text>
              </View>
              <View className="flex-1">
                <Text className="font-semibold text-gray-800">
                  {selectedClient.name}
                </Text>
                {selectedClient.phone && (
                  <Text className="text-gray-600 text-sm">
                    📞 {selectedClient.phone}
                  </Text>
                )}
                {selectedClient.email && (
                  <Text className="text-gray-600 text-sm">
                    ✉️ {selectedClient.email}
                  </Text>
                )}
              </View>
              <TouchableOpacity
                onPress={() => setSelectedClient(null)}
                className="bg-gray-100 p-2 rounded-lg"
              >
                <Edit3 size={16} color="#6B7280" />
              </TouchableOpacity>
            </View>
          </View>

          {/* GDPR Compliant Consent */}
          <View className="bg-white rounded-lg p-4 mb-6">
            <Text className="text-lg font-semibold mb-3">
              Consentimiento Informado (GDPR)
            </Text>
            
            <Text className="text-sm text-gray-600 mb-4">
              Para realizar la consulta ultra-inteligente, necesitamos su consentimiento 
              explícito para los siguientes usos de datos:
            </Text>

            {/* Required Consents */}
            <View className="space-y-3 mb-4">
              <TouchableOpacity
                className={`p-3 rounded-lg border-2 ${
                  consent.photoUsage ? "border-green-500 bg-green-50" : "border-gray-300"
                }`}
                onPress={() => handleConsentChange("photoUsage")}
              >
                <View className="flex-row items-center">
                  <CheckCircle 
                    size={18} 
                    color={consent.photoUsage ? "#10B981" : "#9CA3AF"} 
                  />
                  <Text className="ml-3 font-medium">Uso de Fotografías *</Text>
                </View>
                <Text className="text-xs text-gray-600 mt-1 ml-6">
                  Autorizo la captura y análisis de fotografías de mi cabello para 
                  diagnóstico profesional y formulación de color.
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                className={`p-3 rounded-lg border-2 ${
                  consent.aiAnalysis ? "border-green-500 bg-green-50" : "border-gray-300"
                }`}
                onPress={() => handleConsentChange("aiAnalysis")}
              >
                <View className="flex-row items-center">
                  <CheckCircle 
                    size={18} 
                    color={consent.aiAnalysis ? "#10B981" : "#9CA3AF"} 
                  />
                  <Text className="ml-3 font-medium">Análisis con IA *</Text>
                </View>
                <Text className="text-xs text-gray-600 mt-1 ml-6">
                  Autorizo el procesamiento de mis datos con inteligencia artificial 
                  para análisis de color y recomendaciones personalizadas.
                </Text>
              </TouchableOpacity>
            </View>

            {/* Optional Consents */}
            <Text className="text-sm font-medium text-gray-700 mb-2">
              Consentimientos Opcionales:
            </Text>
            
            <View className="space-y-3 mb-4">
              <TouchableOpacity
                className={`p-3 rounded-lg border ${
                  consent.dataStorage ? "border-blue-500 bg-blue-50" : "border-gray-300"
                }`}
                onPress={() => handleConsentChange("dataStorage")}
              >
                <View className="flex-row items-center">
                  <CheckCircle 
                    size={18} 
                    color={consent.dataStorage ? "#3B82F6" : "#9CA3AF"} 
                  />
                  <Text className="ml-3 font-medium">Almacenamiento de Datos</Text>
                </View>
                <Text className="text-xs text-gray-600 mt-1 ml-6">
                  Autorizo el almacenamiento de mis datos para futuras consultas 
                  y mejora del servicio personalizado.
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                className={`p-3 rounded-lg border ${
                  consent.marketing ? "border-purple-500 bg-purple-50" : "border-gray-300"
                }`}
                onPress={() => handleConsentChange("marketing")}
              >
                <View className="flex-row items-center">
                  <CheckCircle 
                    size={18} 
                    color={consent.marketing ? "#8B5CF6" : "#9CA3AF"} 
                  />
                  <Text className="ml-3 font-medium">Comunicaciones de Marketing</Text>
                </View>
                <Text className="text-xs text-gray-600 mt-1 ml-6">
                  Autorizo recibir ofertas personalizadas y novedades sobre 
                  servicios de coloración.
                </Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Digital Signature */}
          <View className="bg-white rounded-lg p-4 mb-6">
            <Text className="text-lg font-semibold mb-3">Firma Digital</Text>
            
            <TouchableOpacity
              className={`border-2 rounded-lg p-6 ${
                signatureConfirmed ? "border-green-500 bg-green-50" : "border-gray-300 bg-gray-50"
              }`}
              onPress={handleDigitalSignature}
              disabled={!getRequiredConsents()}
            >
              <View className="items-center">
                {signatureConfirmed ? (
                  <>
                    <CheckCircle size={48} color="#10B981" />
                    <Text className="text-green-700 font-semibold mt-2">
                      Firma Digital Confirmada
                    </Text>
                    <Text className="text-green-600 text-sm text-center mt-1">
                      {new Date(signatureTimestamp).toLocaleString()}
                    </Text>
                    <Text className="text-xs text-gray-500 text-center mt-1">
                      Dispositivo: {getDeviceInfo()}
                    </Text>
                  </>
                ) : (
                  <>
                    <Edit3 size={48} color={getRequiredConsents() ? "#3B82F6" : "#9CA3AF"} />
                    <Text className={`font-medium mt-2 ${
                      getRequiredConsents() ? "text-blue-700" : "text-gray-500"
                    }`}>
                      {getRequiredConsents() 
                        ? "Toque para Firmar Digitalmente" 
                        : "Complete los consentimientos obligatorios"
                      }
                    </Text>
                    <Text className="text-xs text-gray-500 text-center mt-1">
                      Confirmación biométrica con registro de timestamp
                    </Text>
                  </>
                )}
              </View>
            </TouchableOpacity>
            
            {signatureConfirmed && (
              <TouchableOpacity
                className="mt-3 p-2 bg-gray-200 rounded-lg"
                onPress={clearSignature}
              >
                <Text className="text-center text-gray-700 text-sm">
                  Limpiar Firma
                </Text>
              </TouchableOpacity>
            )}
          </View>

          {/* Legal Notice */}
          <View className="bg-yellow-50 p-4 rounded-lg mb-6">
            <Text className="text-xs text-yellow-800">
              <Text className="font-semibold">Aviso Legal:</Text> Puede revocar estos 
              consentimientos en cualquier momento. Sus datos se procesan conforme a 
              nuestra política de privacidad y normativa GDPR/LOPD vigente.
            </Text>
          </View>
        </ScrollView>
      )}

      {/* Action Button */}
      {selectedClient && (
        <View className="p-4 bg-white border-t border-gray-200">
          <TouchableOpacity
            className={`py-3 px-4 rounded-lg flex-row items-center justify-center ${
              getRequiredConsents() && signatureConfirmed
                ? "bg-green-500"
                : "bg-gray-300"
            }`}
            onPress={handleConfirmConsent}
            disabled={!getRequiredConsents() || !signatureConfirmed}
          >
            <Shield size={18} color="white" />
            <Text className="text-white font-semibold ml-2">
              {getRequiredConsents() && signatureConfirmed
                ? "Iniciar Consulta Ultra-Inteligente"
                : "Complete consentimiento y firma"
              }
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default ClientConsentPhase;
