import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  TextInput,
} from "react-native";
import {
  Camera,
  FileText,
  CheckCircle,
  Star,
  Clock,
  DollarSign,
  Heart,
  MessageCircle,
  Save,
  Send,
} from "lucide-react-native";
import * as ImagePicker from "expo-image-picker";

interface ResultDocumentationData {
  beforePhotos: Array<{
    id: string;
    uri: string;
    zone: string;
    timestamp: string;
  }>;
  afterPhotos: Array<{
    id: string;
    uri: string;
    zone: string;
    timestamp: string;
    quality: "excellent" | "good" | "fair";
  }>;
  appliedFormula: {
    actualProducts: Array<{
      product: string;
      amount: string;
      notes?: string;
    }>;
    actualProcess: Array<{
      step: string;
      actualTime: number;
      plannedTime: number;
      notes?: string;
    }>;
    deviations: string[];
    totalTime: number;
  };
  results: {
    colorAchieved: string;
    clientSatisfaction: 1 | 2 | 3 | 4 | 5;
    stylistSatisfaction: 1 | 2 | 3 | 4 | 5;
    matchesExpectation: "exceeded" | "met" | "partial" | "not_met";
    durabilityExpected: string;
    maintenanceAdvice: string[];
  };
  clientFeedback: {
    comments: string;
    wouldRecommend: boolean;
    nextAppointment?: string;
    concerns?: string;
  };
  stylistNotes: {
    processNotes: string;
    lessonsLearned: string;
    nextTimeAdjustments: string;
    privateNotes: string;
  };
  followUp: {
    scheduledCheckIn: string;
    maintenanceReminders: string[];
    nextServiceSuggestions: string[];
  };
}

interface ResultDocumentationPhaseProps {
  client: any;
  formulationData: any;
  onComplete: (documentation: ResultDocumentationData) => void;
  onBack?: () => void;
}

const PHOTO_ZONES = [
  { id: "frontal", name: "Frontal", required: true },
  { id: "back", name: "Posterior", required: true },
  { id: "left", name: "Lateral Izq.", required: false },
  { id: "right", name: "Lateral Der.", required: false },
  { id: "detail", name: "Detalle", required: false },
];

const SATISFACTION_LEVELS = [
  { value: 1, label: "Muy insatisfecho", emoji: "😞", color: "text-red-600" },
  { value: 2, label: "Insatisfecho", emoji: "😕", color: "text-orange-600" },
  { value: 3, label: "Neutral", emoji: "😐", color: "text-yellow-600" },
  { value: 4, label: "Satisfecho", emoji: "😊", color: "text-green-600" },
  { value: 5, label: "Muy satisfecho", emoji: "😍", color: "text-green-700" },
];

const EXPECTATION_LEVELS = [
  { value: "exceeded", label: "Superó expectativas", color: "text-green-700" },
  { value: "met", label: "Cumplió expectativas", color: "text-green-600" },
  { value: "partial", label: "Cumplió parcialmente", color: "text-yellow-600" },
  { value: "not_met", label: "No cumplió", color: "text-red-600" },
];

const ResultDocumentationPhase: React.FC<ResultDocumentationPhaseProps> = ({
  client,
  formulationData,
  onComplete,
  onBack,
}) => {
  const [currentStep, setCurrentStep] = useState(1); // 1: Photos, 2: Process, 3: Results, 4: Feedback
  const [afterPhotos, setAfterPhotos] = useState<ResultDocumentationData["afterPhotos"]>([]);
  
  // Applied formula tracking
  const [actualProducts, setActualProducts] = useState<any[]>([]);
  const [processDeviations, setProcessDeviations] = useState<string[]>([]);
  const [totalActualTime, setTotalActualTime] = useState(0);
  
  // Results
  const [colorAchieved, setColorAchieved] = useState("");
  const [clientSatisfaction, setClientSatisfaction] = useState<number>(5);
  const [stylistSatisfaction, setStylistSatisfaction] = useState<number>(5);
  const [matchesExpectation, setMatchesExpectation] = useState<string>("met");
  
  // Feedback
  const [clientComments, setClientComments] = useState("");
  const [wouldRecommend, setWouldRecommend] = useState(true);
  const [stylistNotes, setStylistNotes] = useState("");
  const [lessonsLearned, setLessonsLearned] = useState("");

  const captureAfterPhoto = async (zone: string) => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        aspect: [4, 3],
        quality: 0.9, // Higher quality for final results
      });

      if (!result.canceled && result.assets[0]) {
        const newPhoto = {
          id: `after_${Date.now()}`,
          uri: result.assets[0].uri,
          zone,
          timestamp: new Date().toISOString(),
          quality: "excellent" as const, // Assume high quality for final photos
        };

        setAfterPhotos(prev => [...prev, newPhoto]);
      }
    } catch (error) {
      console.error("Error capturing after photo:", error);
      Alert.alert("Error", "No se pudo capturar la foto del resultado.");
    }
  };

  const removeAfterPhoto = (photoId: string) => {
    setAfterPhotos(prev => prev.filter(photo => photo.id !== photoId));
  };

  const getPhotosForZone = (zone: string) => {
    return afterPhotos.filter(photo => photo.zone === zone);
  };

  const getRequiredPhotosCount = () => {
    const requiredZones = PHOTO_ZONES.filter(zone => zone.required);
    return requiredZones.filter(zone => getPhotosForZone(zone.id).length > 0).length;
  };

  const canProceedFromPhotos = () => {
    return getRequiredPhotosCount() >= 2; // At least frontal and back
  };

  const handleCompleteDocumentation = () => {
    const documentation: ResultDocumentationData = {
      beforePhotos: [], // Would come from previous phases
      afterPhotos,
      appliedFormula: {
        actualProducts: actualProducts.length > 0 ? actualProducts : formulationData?.formula ? [
          {
            product: `${formulationData.formula.baseColor.brand} ${formulationData.formula.baseColor.shade}`,
            amount: formulationData.formula.baseColor.volume,
          },
          {
            product: `Oxidante ${formulationData.formula.developer.volume}`,
            amount: formulationData.formula.developer.amount,
          },
        ] : [],
        actualProcess: formulationData?.process?.steps?.map((step: any, index: number) => ({
          step: step.action,
          actualTime: step.duration, // In real app, track actual time
          plannedTime: step.duration,
          notes: "",
        })) || [],
        deviations: processDeviations,
        totalTime: totalActualTime || formulationData?.process?.totalTime || 60,
      },
      results: {
        colorAchieved: colorAchieved || "Color logrado según expectativas",
        clientSatisfaction: clientSatisfaction as any,
        stylistSatisfaction: stylistSatisfaction as any,
        matchesExpectation: matchesExpectation as any,
        durabilityExpected: "6-8 semanas",
        maintenanceAdvice: [
          "Champú sin sulfatos",
          "Mascarilla hidratante semanal",
          "Protector térmico",
          "Evitar agua muy caliente",
        ],
      },
      clientFeedback: {
        comments: clientComments,
        wouldRecommend,
        concerns: "",
      },
      stylistNotes: {
        processNotes: stylistNotes,
        lessonsLearned,
        nextTimeAdjustments: "",
        privateNotes: "",
      },
      followUp: {
        scheduledCheckIn: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(), // 2 weeks
        maintenanceReminders: [
          "Recordatorio de mantenimiento en 4 semanas",
          "Sugerencia de retoque en 6-8 semanas",
        ],
        nextServiceSuggestions: [
          "Tratamiento hidratante",
          "Retoque de color",
        ],
      },
    };

    onComplete(documentation);
  };

  const renderPhotosStep = () => (
    <View className="flex-1">
      <View className="p-4">
        <Text className="text-xl font-bold text-gray-800 mb-2">
          Fotos del Resultado
        </Text>
        <Text className="text-gray-600">
          Documenta el resultado final con fotos de calidad profesional
        </Text>
      </View>

      <ScrollView className="flex-1 px-4">
        <View className="bg-blue-50 p-4 rounded-lg mb-4">
          <Text className="font-semibold text-blue-900 mb-2">
            📸 Consejos para Fotos Profesionales
          </Text>
          <Text className="text-blue-800 text-sm">
            • Usa la misma iluminación que las fotos iniciales{"\n"}
            • Captura los mismos ángulos para comparación{"\n"}
            • Asegúrate de que el cabello esté peinado{"\n"}
            • Evita sombras en el resultado final
          </Text>
        </View>

        {PHOTO_ZONES.map((zone) => {
          const zonePhotos = getPhotosForZone(zone.id);
          return (
            <View key={zone.id} className="bg-white rounded-lg p-4 mb-3">
              <View className="flex-row items-center justify-between mb-2">
                <View>
                  <Text className="font-medium">
                    {zone.name}
                    {zone.required && <Text className="text-red-500"> *</Text>}
                  </Text>
                </View>
                <TouchableOpacity
                  onPress={() => captureAfterPhoto(zone.id)}
                  className="bg-green-500 px-3 py-2 rounded-lg flex-row items-center"
                >
                  <Camera size={16} color="white" />
                  <Text className="text-white text-sm ml-1">Capturar</Text>
                </TouchableOpacity>
              </View>

              {zonePhotos.length > 0 && (
                <View className="flex-row flex-wrap mt-2">
                  {zonePhotos.map((photo) => (
                    <View key={photo.id} className="w-20 h-20 mr-2 mb-2 relative">
                      <Image
                        source={{ uri: photo.uri }}
                        className="w-full h-full rounded-lg"
                        resizeMode="cover"
                      />
                      <TouchableOpacity
                        onPress={() => removeAfterPhoto(photo.id)}
                        className="absolute top-1 right-1 bg-red-500 rounded-full p-1"
                      >
                        <Text className="text-white text-xs">×</Text>
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              )}
            </View>
          );
        })}

        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="font-semibold mb-2">Progreso</Text>
          <Text className="text-sm text-gray-600 mb-2">
            {getRequiredPhotosCount()}/2 zonas obligatorias • {afterPhotos.length} fotos totales
          </Text>
          <View className="bg-gray-200 rounded-full h-2">
            <View
              className="bg-green-500 rounded-full h-2"
              style={{ width: `${(getRequiredPhotosCount() / 2) * 100}%` }}
            />
          </View>
        </View>
      </ScrollView>

      <View className="p-4 bg-white border-t border-gray-200">
        <TouchableOpacity
          className={`py-3 px-4 rounded-lg flex-row items-center justify-center ${
            canProceedFromPhotos() ? "bg-green-500" : "bg-gray-300"
          }`}
          onPress={() => setCurrentStep(2)}
          disabled={!canProceedFromPhotos()}
        >
          <Text className="text-white font-semibold">
            {canProceedFromPhotos() 
              ? "Continuar a Evaluación"
              : `Captura ${2 - getRequiredPhotosCount()} fotos más`
            }
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderResultsStep = () => (
    <View className="flex-1">
      <View className="p-4">
        <Text className="text-xl font-bold text-gray-800 mb-2">
          Evaluación del Resultado
        </Text>
        <Text className="text-gray-600">
          Evalúa la satisfacción y el cumplimiento de expectativas
        </Text>
      </View>

      <ScrollView className="flex-1 px-4">
        {/* Color Achieved */}
        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="text-lg font-semibold mb-2">Color Logrado</Text>
          <TextInput
            className="border border-gray-300 rounded-lg p-3 bg-white"
            placeholder="Describe el color final obtenido..."
            value={colorAchieved}
            onChangeText={setColorAchieved}
            multiline
            numberOfLines={2}
          />
        </View>

        {/* Client Satisfaction */}
        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="text-lg font-semibold mb-3">Satisfacción del Cliente</Text>
          <View className="flex-row justify-between">
            {SATISFACTION_LEVELS.map((level) => (
              <TouchableOpacity
                key={level.value}
                onPress={() => setClientSatisfaction(level.value)}
                className={`items-center p-2 rounded-lg ${
                  clientSatisfaction === level.value ? "bg-blue-100" : "bg-gray-50"
                }`}
              >
                <Text className="text-2xl mb-1">{level.emoji}</Text>
                <Text className={`text-xs text-center ${
                  clientSatisfaction === level.value ? "text-blue-800" : "text-gray-600"
                }`}>
                  {level.value}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Stylist Satisfaction */}
        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="text-lg font-semibold mb-3">Tu Satisfacción Profesional</Text>
          <View className="flex-row justify-between">
            {SATISFACTION_LEVELS.map((level) => (
              <TouchableOpacity
                key={level.value}
                onPress={() => setStylistSatisfaction(level.value)}
                className={`items-center p-2 rounded-lg ${
                  stylistSatisfaction === level.value ? "bg-green-100" : "bg-gray-50"
                }`}
              >
                <Text className="text-2xl mb-1">{level.emoji}</Text>
                <Text className={`text-xs text-center ${
                  stylistSatisfaction === level.value ? "text-green-800" : "text-gray-600"
                }`}>
                  {level.value}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Expectation Match */}
        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="text-lg font-semibold mb-3">Cumplimiento de Expectativas</Text>
          <View className="space-y-2">
            {EXPECTATION_LEVELS.map((level) => (
              <TouchableOpacity
                key={level.value}
                onPress={() => setMatchesExpectation(level.value)}
                className={`p-3 rounded-lg border-2 ${
                  matchesExpectation === level.value
                    ? "border-blue-500 bg-blue-50"
                    : "border-gray-300"
                }`}
              >
                <Text className={`font-medium ${
                  matchesExpectation === level.value ? "text-blue-800" : "text-gray-800"
                }`}>
                  {level.label}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>

      <View className="p-4 bg-white border-t border-gray-200">
        <View className="flex-row space-x-3">
          <TouchableOpacity
            className="flex-1 bg-gray-200 py-3 px-4 rounded-lg"
            onPress={() => setCurrentStep(1)}
          >
            <Text className="text-gray-700 font-semibold text-center">Volver</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className="flex-1 bg-blue-500 py-3 px-4 rounded-lg"
            onPress={() => setCurrentStep(3)}
          >
            <Text className="text-white font-semibold text-center">Continuar</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  const renderFeedbackStep = () => (
    <View className="flex-1">
      <View className="p-4">
        <Text className="text-xl font-bold text-gray-800 mb-2">
          Feedback y Notas
        </Text>
        <Text className="text-gray-600">
          Registra comentarios del cliente y observaciones profesionales
        </Text>
      </View>

      <ScrollView className="flex-1 px-4">
        {/* Client Comments */}
        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="text-lg font-semibold mb-2">Comentarios del Cliente</Text>
          <TextInput
            className="border border-gray-300 rounded-lg p-3 bg-white"
            placeholder="¿Qué opina el cliente sobre el resultado?"
            value={clientComments}
            onChangeText={setClientComments}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
        </View>

        {/* Would Recommend */}
        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="text-lg font-semibold mb-3">Recomendación</Text>
          <View className="flex-row space-x-3">
            <TouchableOpacity
              onPress={() => setWouldRecommend(true)}
              className={`flex-1 p-3 rounded-lg border-2 ${
                wouldRecommend ? "border-green-500 bg-green-50" : "border-gray-300"
              }`}
            >
              <Text className={`text-center font-medium ${
                wouldRecommend ? "text-green-800" : "text-gray-700"
              }`}>
                ✅ Recomendaría el servicio
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => setWouldRecommend(false)}
              className={`flex-1 p-3 rounded-lg border-2 ${
                !wouldRecommend ? "border-red-500 bg-red-50" : "border-gray-300"
              }`}
            >
              <Text className={`text-center font-medium ${
                !wouldRecommend ? "text-red-800" : "text-gray-700"
              }`}>
                ❌ No recomendaría
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Stylist Notes */}
        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="text-lg font-semibold mb-2">Notas del Proceso</Text>
          <TextInput
            className="border border-gray-300 rounded-lg p-3 bg-white"
            placeholder="Observaciones sobre el proceso, productos utilizados, tiempos..."
            value={stylistNotes}
            onChangeText={setStylistNotes}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
        </View>

        {/* Lessons Learned */}
        <View className="bg-white rounded-lg p-4 mb-6">
          <Text className="text-lg font-semibold mb-2">Aprendizajes</Text>
          <TextInput
            className="border border-gray-300 rounded-lg p-3 bg-white"
            placeholder="¿Qué harías diferente la próxima vez? ¿Qué funcionó especialmente bien?"
            value={lessonsLearned}
            onChangeText={setLessonsLearned}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
        </View>
      </ScrollView>

      <View className="p-4 bg-white border-t border-gray-200">
        <View className="flex-row space-x-3">
          <TouchableOpacity
            className="flex-1 bg-gray-200 py-3 px-4 rounded-lg"
            onPress={() => setCurrentStep(2)}
          >
            <Text className="text-gray-700 font-semibold text-center">Volver</Text>
          </TouchableOpacity>
          <TouchableOpacity
            className="flex-1 bg-green-500 py-3 px-4 rounded-lg flex-row items-center justify-center"
            onPress={handleCompleteDocumentation}
          >
            <Save size={18} color="white" />
            <Text className="text-white font-semibold ml-2">
              Completar Consulta
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );

  return (
    <View className="flex-1 bg-gray-50">
      {/* Step Indicator */}
      <View className="bg-white p-4 border-b border-gray-200">
        <View className="flex-row justify-between mb-2">
          {[1, 2, 3].map((step) => (
            <View key={step} className="items-center flex-1">
              <View className={`w-8 h-8 rounded-full items-center justify-center ${
                currentStep >= step ? "bg-green-500" : "bg-gray-300"
              }`}>
                <Text className="text-white font-bold text-sm">{step}</Text>
              </View>
              <Text className="text-xs mt-1 text-center">
                {step === 1 ? "Fotos" : step === 2 ? "Evaluación" : "Feedback"}
              </Text>
            </View>
          ))}
        </View>
      </View>

      {currentStep === 1 && renderPhotosStep()}
      {currentStep === 2 && renderResultsStep()}
      {currentStep === 3 && renderFeedbackStep()}
    </View>
  );
};

export default ResultDocumentationPhase;
