import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  TextInput,
} from "react-native";
import {
  Camera,
  Upload,
  Palette,
  Zap,
  CheckCircle,
  Edit3,
  Heart,
  Star,
  Lightbulb,
} from "lucide-react-native";
import * as ImagePicker from "expo-image-picker";

interface DesiredColorData {
  referencePhotos: Array<{
    id: string;
    uri: string;
    source: "camera" | "gallery";
    timestamp: string;
    aiAnalysis?: {
      dominantColor: string;
      undertone: string;
      lightness: number;
      saturation: number;
      complexity: "simple" | "medium" | "complex";
    };
  }>;
  verbalDescription: string;
  colorPalette: {
    primary: string;
    secondary?: string;
    highlights?: string;
    technique: "solid" | "highlights" | "balayage" | "ombre" | "babylights";
  };
  aiAnalysis: {
    feasibility: "high" | "medium" | "low";
    sessionsRequired: number;
    riskLevel: "low" | "medium" | "high";
    recommendations: string[];
    confidence: number;
  };
  stylistValidation: {
    validated: boolean;
    modifications: Record<string, any>;
    notes: string;
    finalApproval: boolean;
  };
}

interface DesiredColorAnalysisPhaseProps {
  client: any;
  currentColorAnalysis: any;
  onComplete: (analysis: DesiredColorData) => void;
  onBack?: () => void;
}

const COLOR_TECHNIQUES = [
  { id: "solid", name: "Color Sólido", description: "Color uniforme en todo el cabello" },
  { id: "highlights", name: "Mechas", description: "Mechas tradicionales con gorro o papel" },
  { id: "balayage", name: "Balayage", description: "Técnica a mano alzada natural" },
  { id: "ombre", name: "Ombré", description: "Degradado de oscuro a claro" },
  { id: "babylights", name: "Babylights", description: "Mechas muy finas y naturales" },
];

const POPULAR_COLORS = [
  { name: "Rubio Platino", hex: "#F5F5DC", undertone: "cool" },
  { name: "Rubio Miel", hex: "#DAA520", undertone: "warm" },
  { name: "Castaño Chocolate", hex: "#7B3F00", undertone: "neutral" },
  { name: "Pelirrojo Cobrizo", hex: "#B87333", undertone: "warm" },
  { name: "Negro Azulado", hex: "#1C1C1C", undertone: "cool" },
  { name: "Caramelo", hex: "#CD853F", undertone: "warm" },
];

const DesiredColorAnalysisPhase: React.FC<DesiredColorAnalysisPhaseProps> = ({
  client,
  currentColorAnalysis,
  onComplete,
  onBack,
}) => {
  const [referencePhotos, setReferencePhotos] = useState<DesiredColorData["referencePhotos"]>([]);
  const [verbalDescription, setVerbalDescription] = useState("");
  const [selectedTechnique, setSelectedTechnique] = useState<string>("solid");
  const [selectedColors, setSelectedColors] = useState<string[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [aiAnalysis, setAiAnalysis] = useState<DesiredColorData["aiAnalysis"] | null>(null);
  const [showValidation, setShowValidation] = useState(false);
  const [stylistValidation, setStylistValidation] = useState<DesiredColorData["stylistValidation"]>({
    validated: false,
    modifications: {},
    notes: "",
    finalApproval: false,
  });

  const captureReferencePhoto = async () => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const newPhoto = {
          id: `ref_${Date.now()}`,
          uri: result.assets[0].uri,
          source: "camera" as const,
          timestamp: new Date().toISOString(),
        };

        setReferencePhotos(prev => [...prev, newPhoto]);
      }
    } catch (error) {
      console.error("Error capturing reference photo:", error);
      Alert.alert("Error", "No se pudo capturar la foto de referencia.");
    }
  };

  const selectFromGallery = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const newPhoto = {
          id: `ref_${Date.now()}`,
          uri: result.assets[0].uri,
          source: "gallery" as const,
          timestamp: new Date().toISOString(),
        };

        setReferencePhotos(prev => [...prev, newPhoto]);
      }
    } catch (error) {
      console.error("Error selecting reference photo:", error);
      Alert.alert("Error", "No se pudo seleccionar la foto de referencia.");
    }
  };

  const removeReferencePhoto = (photoId: string) => {
    setReferencePhotos(prev => prev.filter(photo => photo.id !== photoId));
  };

  const showPhotoOptions = () => {
    Alert.alert(
      "Añadir Referencia",
      "¿Cómo quieres añadir la imagen de referencia del color deseado?",
      [
        { text: "Tomar Foto", onPress: captureReferencePhoto },
        { text: "Elegir de Galería", onPress: selectFromGallery },
        { text: "Cancelar", style: "cancel" },
      ]
    );
  };

  const toggleColorSelection = (colorName: string) => {
    setSelectedColors(prev => {
      if (prev.includes(colorName)) {
        return prev.filter(c => c !== colorName);
      } else {
        return [...prev, colorName];
      }
    });
  };

  const canAnalyze = () => {
    return referencePhotos.length > 0 || verbalDescription.trim().length > 10 || selectedColors.length > 0;
  };

  const simulateDesiredColorAnalysis = async (): Promise<DesiredColorData["aiAnalysis"]> => {
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Analyze feasibility based on current color vs desired color
    const currentLevel = currentColorAnalysis?.aiAnalysis?.naturalLevel || 4;
    const currentCondition = currentColorAnalysis?.aiAnalysis?.condition || "good";
    
    let feasibility: "high" | "medium" | "low" = "high";
    let sessionsRequired = 1;
    let riskLevel: "low" | "medium" | "high" = "low";
    const recommendations: string[] = [];

    // Determine complexity based on color change
    if (selectedColors.some(color => color.includes("Platino"))) {
      if (currentLevel < 6) {
        feasibility = "medium";
        sessionsRequired = 2;
        riskLevel = "medium";
        recommendations.push("Requiere decoloración previa");
        recommendations.push("Usar tratamiento protector");
      }
    }

    if (currentCondition === "poor" || currentCondition === "damaged") {
      riskLevel = "high";
      recommendations.push("Realizar tratamiento reconstructor antes");
      recommendations.push("Considerar proceso gradual");
    }

    if (selectedTechnique === "balayage" || selectedTechnique === "ombre") {
      recommendations.push("Técnica ideal para transición natural");
    }

    return {
      feasibility,
      sessionsRequired,
      riskLevel,
      recommendations,
      confidence: 87,
    };
  };

  const handleAnalyzeDesiredColor = async () => {
    if (!canAnalyze()) {
      Alert.alert(
        "Información Insuficiente",
        "Añade al menos una foto de referencia, descripción verbal o selecciona colores de la paleta."
      );
      return;
    }

    setIsAnalyzing(true);

    try {
      const analysis = await simulateDesiredColorAnalysis();
      setAiAnalysis(analysis);
      setShowValidation(true);
    } catch (error) {
      console.error("Error during desired color analysis:", error);
      Alert.alert("Error", "No se pudo completar el análisis del color deseado.");
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleFinalApproval = () => {
    if (!aiAnalysis) return;

    const finalAnalysis: DesiredColorData = {
      referencePhotos,
      verbalDescription,
      colorPalette: {
        primary: selectedColors[0] || "Por definir",
        secondary: selectedColors[1],
        highlights: selectedColors[2],
        technique: selectedTechnique as any,
      },
      aiAnalysis: {
        ...aiAnalysis,
        ...stylistValidation.modifications,
      },
      stylistValidation: {
        ...stylistValidation,
        finalApproval: true,
      },
    };

    onComplete(finalAnalysis);
  };

  const getFeasibilityColor = (feasibility: string) => {
    switch (feasibility) {
      case "high": return "text-green-600";
      case "medium": return "text-yellow-600";
      case "low": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "low": return "text-green-600";
      case "medium": return "text-yellow-600";
      case "high": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  if (isAnalyzing) {
    return (
      <View className="flex-1 bg-gray-50 justify-center items-center">
        <Palette size={48} color="#8B5CF6" className="mb-4" />
        <Text className="text-lg font-semibold text-gray-800 mb-2">
          Analizando Color Deseado con IA
        </Text>
        <Text className="text-gray-600 text-center px-8 mb-4">
          Evaluando viabilidad, riesgo y recomendaciones para el color objetivo...
        </Text>
        <View className="w-64 bg-gray-200 rounded-full h-2">
          <View className="bg-purple-500 rounded-full h-2 w-3/4 animate-pulse" />
        </View>
      </View>
    );
  }

  if (showValidation && aiAnalysis) {
    return (
      <View className="flex-1 bg-gray-50">
        <View className="p-4">
          <Text className="text-2xl font-bold text-gray-800 mb-2">
            Validación Color Deseado
          </Text>
          <Text className="text-gray-600">
            {client?.name} - Revisa el análisis del objetivo de color
          </Text>
        </View>

        <ScrollView className="flex-1 px-4">
          {/* Feasibility Analysis */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <Text className="text-lg font-semibold mb-3">Análisis de Viabilidad</Text>
            
            <View className="space-y-3">
              <View className="flex-row justify-between items-center">
                <Text className="text-gray-700">Viabilidad:</Text>
                <Text className={`font-bold capitalize ${getFeasibilityColor(aiAnalysis.feasibility)}`}>
                  {aiAnalysis.feasibility}
                </Text>
              </View>

              <View className="flex-row justify-between items-center">
                <Text className="text-gray-700">Sesiones Requeridas:</Text>
                <Text className="font-medium">{aiAnalysis.sessionsRequired}</Text>
              </View>

              <View className="flex-row justify-between items-center">
                <Text className="text-gray-700">Nivel de Riesgo:</Text>
                <Text className={`font-bold capitalize ${getRiskColor(aiAnalysis.riskLevel)}`}>
                  {aiAnalysis.riskLevel}
                </Text>
              </View>

              <View className="flex-row justify-between items-center">
                <Text className="text-gray-700">Confianza IA:</Text>
                <Text className="font-medium">{aiAnalysis.confidence}%</Text>
              </View>
            </View>
          </View>

          {/* Recommendations */}
          {aiAnalysis.recommendations.length > 0 && (
            <View className="bg-white rounded-lg p-4 mb-4">
              <Text className="text-lg font-semibold mb-3">Recomendaciones IA</Text>
              {aiAnalysis.recommendations.map((rec, index) => (
                <View key={index} className="flex-row items-start mb-2">
                  <Lightbulb size={16} color="#F59E0B" className="mt-1 mr-2" />
                  <Text className="text-gray-700 flex-1">{rec}</Text>
                </View>
              ))}
            </View>
          )}

          {/* Selected Technique */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <Text className="text-lg font-semibold mb-2">Técnica Seleccionada</Text>
            <Text className="text-gray-700 capitalize font-medium">
              {COLOR_TECHNIQUES.find(t => t.id === selectedTechnique)?.name}
            </Text>
            <Text className="text-sm text-gray-600">
              {COLOR_TECHNIQUES.find(t => t.id === selectedTechnique)?.description}
            </Text>
          </View>

          {/* Color Palette */}
          {selectedColors.length > 0 && (
            <View className="bg-white rounded-lg p-4 mb-4">
              <Text className="text-lg font-semibold mb-2">Paleta de Colores</Text>
              <View className="flex-row flex-wrap">
                {selectedColors.map((color, index) => (
                  <View key={index} className="bg-purple-100 px-3 py-1 rounded-full mr-2 mb-2">
                    <Text className="text-purple-800 text-sm">{color}</Text>
                  </View>
                ))}
              </View>
            </View>
          )}

          {/* Verbal Description */}
          {verbalDescription.trim() && (
            <View className="bg-white rounded-lg p-4 mb-4">
              <Text className="text-lg font-semibold mb-2">Descripción del Cliente</Text>
              <Text className="text-gray-700 italic">"{verbalDescription}"</Text>
            </View>
          )}

          {/* Stylist Notes */}
          <View className="bg-white rounded-lg p-4 mb-6">
            <Text className="text-lg font-semibold mb-2">Notas del Estilista</Text>
            <Text className="text-sm text-gray-600 mb-2">
              Observaciones sobre el objetivo de color:
            </Text>
            <TouchableOpacity className="border border-gray-300 rounded-lg p-3 bg-gray-50">
              <Text className="text-gray-500">Toca para añadir notas...</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>

        {/* Action Buttons */}
        <View className="p-4 bg-white border-t border-gray-200">
          <View className="flex-row space-x-3">
            <TouchableOpacity
              className="flex-1 bg-gray-200 py-3 px-4 rounded-lg"
              onPress={() => setShowValidation(false)}
            >
              <Text className="text-gray-700 font-semibold text-center">
                Volver a Editar
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              className="flex-1 bg-purple-500 py-3 px-4 rounded-lg flex-row items-center justify-center"
              onPress={handleFinalApproval}
            >
              <CheckCircle size={18} color="white" />
              <Text className="text-white font-semibold ml-2">
                Confirmar Objetivo
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      <View className="p-4">
        <Text className="text-2xl font-bold text-gray-800 mb-2">
          Color Deseado
        </Text>
        <Text className="text-gray-600">
          {client?.name} - Define el objetivo de color con referencias y análisis IA
        </Text>
      </View>

      <ScrollView className="flex-1 px-4">
        {/* Reference Photos */}
        <View className="bg-white rounded-lg p-4 mb-4">
          <View className="flex-row items-center justify-between mb-3">
            <Text className="text-lg font-semibold">Fotos de Referencia</Text>
            <TouchableOpacity
              onPress={showPhotoOptions}
              className="bg-purple-500 px-3 py-2 rounded-lg flex-row items-center"
            >
              <Camera size={16} color="white" />
              <Text className="text-white text-sm ml-1">Añadir</Text>
            </TouchableOpacity>
          </View>

          <Text className="text-sm text-gray-600 mb-3">
            Máximo 3 imágenes de referencia del color deseado
          </Text>

          {referencePhotos.length > 0 ? (
            <View className="flex-row flex-wrap">
              {referencePhotos.map((photo) => (
                <View key={photo.id} className="w-24 h-24 mr-2 mb-2 relative">
                  <Image
                    source={{ uri: photo.uri }}
                    className="w-full h-full rounded-lg"
                    resizeMode="cover"
                  />
                  <TouchableOpacity
                    onPress={() => removeReferencePhoto(photo.id)}
                    className="absolute top-1 right-1 bg-red-500 rounded-full p-1"
                  >
                    <Text className="text-white text-xs">×</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
          ) : (
            <View className="border-2 border-dashed border-gray-300 rounded-lg p-6 items-center">
              <Upload size={32} color="#9CA3AF" />
              <Text className="text-gray-500 text-center mt-2">
                Añade fotos de referencia del color que deseas lograr
              </Text>
            </View>
          )}
        </View>

        {/* Verbal Description */}
        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="text-lg font-semibold mb-2">Descripción Verbal</Text>
          <Text className="text-sm text-gray-600 mb-3">
            Describe con palabras el color que el cliente desea
          </Text>
          <TextInput
            className="border border-gray-300 rounded-lg p-3 bg-white"
            placeholder="Ej: Quiero un rubio miel con mechas más claras en el contorno..."
            value={verbalDescription}
            onChangeText={setVerbalDescription}
            multiline
            numberOfLines={3}
            textAlignVertical="top"
          />
        </View>

        {/* Color Palette */}
        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="text-lg font-semibold mb-2">Paleta de Colores</Text>
          <Text className="text-sm text-gray-600 mb-3">
            Selecciona colores que se acerquen al objetivo
          </Text>
          <View className="flex-row flex-wrap">
            {POPULAR_COLORS.map((color) => (
              <TouchableOpacity
                key={color.name}
                onPress={() => toggleColorSelection(color.name)}
                className={`m-1 px-3 py-2 rounded-lg border-2 ${
                  selectedColors.includes(color.name)
                    ? "border-purple-500 bg-purple-100"
                    : "border-gray-300 bg-white"
                }`}
              >
                <View className="flex-row items-center">
                  <View
                    className="w-4 h-4 rounded-full mr-2"
                    style={{ backgroundColor: color.hex }}
                  />
                  <Text className={`text-sm ${
                    selectedColors.includes(color.name) ? "text-purple-800" : "text-gray-700"
                  }`}>
                    {color.name}
                  </Text>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Technique Selection */}
        <View className="bg-white rounded-lg p-4 mb-6">
          <Text className="text-lg font-semibold mb-2">Técnica de Aplicación</Text>
          <Text className="text-sm text-gray-600 mb-3">
            Selecciona la técnica más adecuada
          </Text>
          <View className="space-y-2">
            {COLOR_TECHNIQUES.map((technique) => (
              <TouchableOpacity
                key={technique.id}
                onPress={() => setSelectedTechnique(technique.id)}
                className={`p-3 rounded-lg border-2 ${
                  selectedTechnique === technique.id
                    ? "border-purple-500 bg-purple-50"
                    : "border-gray-300"
                }`}
              >
                <Text className={`font-medium ${
                  selectedTechnique === technique.id ? "text-purple-800" : "text-gray-800"
                }`}>
                  {technique.name}
                </Text>
                <Text className="text-sm text-gray-600">{technique.description}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
      </ScrollView>

      {/* Analyze Button */}
      <View className="p-4 bg-white border-t border-gray-200">
        <TouchableOpacity
          className={`py-3 px-4 rounded-lg flex-row items-center justify-center ${
            canAnalyze() ? "bg-purple-500" : "bg-gray-300"
          }`}
          onPress={handleAnalyzeDesiredColor}
          disabled={!canAnalyze()}
        >
          <Zap size={18} color="white" />
          <Text className="text-white font-semibold ml-2">
            {canAnalyze() 
              ? "Analizar Viabilidad con IA"
              : "Añade referencias o descripción"
            }
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default DesiredColorAnalysisPhase;
