import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
} from "react-native";
import {
  Camera,
  Upload,
  Eye,
  Zap,
  CheckCircle,
  Edit3,
  RefreshCw,
  AlertTriangle,
} from "lucide-react-native";
import * as ImagePicker from "expo-image-picker";

interface CurrentColorData {
  photos: Array<{
    id: string;
    uri: string;
    zone: "frontal" | "crown" | "back" | "left" | "right";
    timestamp: string;
    quality: "high" | "medium" | "low";
  }>;
  aiAnalysis: {
    naturalLevel: number;
    undertone: "warm" | "cool" | "neutral";
    grayPercentage: number;
    condition: "excellent" | "good" | "fair" | "poor" | "damaged";
    porosity: "low" | "medium" | "high";
    density: "low" | "medium" | "high";
    diameter: "fine" | "medium" | "coarse";
    elasticity: "excellent" | "good" | "poor";
    existingColor: string;
    previousTreatments: string[];
    confidence: number;
  };
  stylistValidation: {
    validated: boolean;
    modifications: Record<string, any>;
    notes: string;
    finalApproval: boolean;
  };
}

interface CurrentColorAnalysisPhaseProps {
  client: any;
  onComplete: (analysis: CurrentColorData) => void;
  onBack?: () => void;
}

const REQUIRED_ZONES = [
  { id: "frontal", name: "Frontal", description: "Vista frontal del cabello", required: true },
  { id: "crown", name: "Coronilla", description: "Parte superior de la cabeza", required: true },
  { id: "back", name: "Nuca", description: "Parte posterior del cabello", required: true },
  { id: "left", name: "Lateral Izq.", description: "Vista lateral izquierda", required: false },
  { id: "right", name: "Lateral Der.", description: "Vista lateral derecha", required: false },
];

const CurrentColorAnalysisPhase: React.FC<CurrentColorAnalysisPhaseProps> = ({
  client,
  onComplete,
  onBack,
}) => {
  const [photos, setPhotos] = useState<CurrentColorData["photos"]>([]);
  const [aiAnalysis, setAiAnalysis] = useState<CurrentColorData["aiAnalysis"] | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [showValidation, setShowValidation] = useState(false);
  const [stylistValidation, setStylistValidation] = useState<CurrentColorData["stylistValidation"]>({
    validated: false,
    modifications: {},
    notes: "",
    finalApproval: false,
  });

  const capturePhoto = async (zone: string) => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const newPhoto = {
          id: `photo_${Date.now()}`,
          uri: result.assets[0].uri,
          zone: zone as any,
          timestamp: new Date().toISOString(),
          quality: validateImageQuality(result.assets[0].uri),
        };

        setPhotos(prev => [...prev, newPhoto]);
      }
    } catch (error) {
      console.error("Error capturing photo:", error);
      Alert.alert("Error", "No se pudo capturar la foto.");
    }
  };

  const selectFromGallery = async (zone: string) => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const newPhoto = {
          id: `photo_${Date.now()}`,
          uri: result.assets[0].uri,
          zone: zone as any,
          timestamp: new Date().toISOString(),
          quality: validateImageQuality(result.assets[0].uri),
        };

        setPhotos(prev => [...prev, newPhoto]);
      }
    } catch (error) {
      console.error("Error selecting photo:", error);
      Alert.alert("Error", "No se pudo seleccionar la foto.");
    }
  };

  const validateImageQuality = (imageUri: string): "high" | "medium" | "low" => {
    // Simplified quality validation
    const random = Math.random();
    if (random > 0.7) return "high";
    if (random > 0.4) return "medium";
    return "low";
  };

  const removePhoto = (photoId: string) => {
    setPhotos(prev => prev.filter(photo => photo.id !== photoId));
  };

  const showPhotoOptions = (zone: string, zoneName: string) => {
    Alert.alert(
      `Capturar ${zoneName}`,
      "Elige cómo quieres añadir la foto del color actual",
      [
        { text: "Tomar Foto", onPress: () => capturePhoto(zone) },
        { text: "Elegir de Galería", onPress: () => selectFromGallery(zone) },
        { text: "Cancelar", style: "cancel" },
      ]
    );
  };

  const getPhotosForZone = (zone: string) => {
    return photos.filter(photo => photo.zone === zone);
  };

  const getRequiredPhotosCount = () => {
    const requiredZones = REQUIRED_ZONES.filter(zone => zone.required);
    return requiredZones.filter(zone => getPhotosForZone(zone.id).length > 0).length;
  };

  const canAnalyze = () => {
    return getRequiredPhotosCount() >= 3; // Minimum 3 required zones
  };

  const simulateAIAnalysis = async (): Promise<CurrentColorData["aiAnalysis"]> => {
    const steps = [
      "Detectando características del cabello...",
      "Analizando nivel natural...",
      "Evaluando subtono dominante...",
      "Calculando porcentaje de canas...",
      "Determinando condición y porosidad...",
      "Identificando tratamientos previos...",
      "Generando análisis completo...",
    ];

    for (let i = 0; i < steps.length; i++) {
      setAnalysisProgress((i + 1) / steps.length * 100);
      await new Promise(resolve => setTimeout(resolve, 800));
    }

    // Generate realistic analysis based on photos
    const mockAnalysis: CurrentColorData["aiAnalysis"] = {
      naturalLevel: 4,
      undertone: "warm",
      grayPercentage: 15,
      condition: "good",
      porosity: "medium",
      density: "medium",
      diameter: "medium",
      elasticity: "good",
      existingColor: "Castaño medio con reflejos dorados",
      previousTreatments: ["Coloración permanente", "Mechas"],
      confidence: 89,
    };

    return mockAnalysis;
  };

  const handleAnalyzeWithAI = async () => {
    if (!canAnalyze()) {
      Alert.alert(
        "Fotos Insuficientes",
        "Se requieren al menos 3 fotos de las zonas obligatorias para un análisis preciso."
      );
      return;
    }

    setIsAnalyzing(true);
    setAnalysisProgress(0);

    try {
      const analysis = await simulateAIAnalysis();
      setAiAnalysis(analysis);
      setShowValidation(true);
    } catch (error) {
      console.error("Error during AI analysis:", error);
      Alert.alert("Error", "No se pudo completar el análisis IA.");
    } finally {
      setIsAnalyzing(false);
      setAnalysisProgress(0);
    }
  };

  const handleStylistValidation = (field: string, value: any) => {
    setStylistValidation(prev => ({
      ...prev,
      modifications: { ...prev.modifications, [field]: value },
      validated: true,
    }));
  };

  const handleFinalApproval = () => {
    if (!aiAnalysis) return;

    const finalAnalysis: CurrentColorData = {
      photos,
      aiAnalysis: {
        ...aiAnalysis,
        ...stylistValidation.modifications,
      },
      stylistValidation: {
        ...stylistValidation,
        finalApproval: true,
      },
    };

    onComplete(finalAnalysis);
  };

  const getQualityColor = (quality: string) => {
    switch (quality) {
      case "high": return "text-green-600";
      case "medium": return "text-yellow-600";
      case "low": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 85) return "text-green-600";
    if (confidence >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  if (isAnalyzing) {
    return (
      <View className="flex-1 bg-gray-50 justify-center items-center">
        <Zap size={48} color="#3B82F6" className="mb-4" />
        <Text className="text-lg font-semibold text-gray-800 mb-2">
          Analizando Color Actual con IA
        </Text>
        <Text className="text-gray-600 text-center px-8 mb-4">
          Procesando {photos.length} imágenes para análisis exhaustivo del color actual...
        </Text>
        <View className="w-64 bg-gray-200 rounded-full h-2 mb-2">
          <View
            className="bg-blue-500 rounded-full h-2 transition-all duration-300"
            style={{ width: `${analysisProgress}%` }}
          />
        </View>
        <Text className="text-sm text-gray-500">
          {Math.round(analysisProgress)}% completado
        </Text>
      </View>
    );
  }

  if (showValidation && aiAnalysis) {
    return (
      <View className="flex-1 bg-gray-50">
        <View className="p-4">
          <Text className="text-2xl font-bold text-gray-800 mb-2">
            Validación del Análisis IA
          </Text>
          <Text className="text-gray-600">
            {client?.name} - Revisa y confirma el análisis del color actual
          </Text>
        </View>

        <ScrollView className="flex-1 px-4">
          {/* AI Confidence */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <View className="flex-row items-center justify-between mb-2">
              <Text className="font-semibold">Confianza del Análisis IA</Text>
              <Text className={`font-bold ${getConfidenceColor(aiAnalysis.confidence)}`}>
                {aiAnalysis.confidence}%
              </Text>
            </View>
            <View className="bg-gray-200 rounded-full h-2">
              <View
                className="bg-blue-500 rounded-full h-2"
                style={{ width: `${aiAnalysis.confidence}%` }}
              />
            </View>
          </View>

          {/* Analysis Results */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <Text className="text-lg font-semibold mb-3">Resultados del Análisis</Text>
            
            <View className="space-y-3">
              <View className="flex-row justify-between items-center">
                <Text className="text-gray-700">Nivel Natural:</Text>
                <View className="flex-row items-center">
                  <Text className="font-medium mr-2">{aiAnalysis.naturalLevel}</Text>
                  <TouchableOpacity
                    onPress={() => {/* Open level selector */}}
                    className="bg-blue-100 p-1 rounded"
                  >
                    <Edit3 size={14} color="#3B82F6" />
                  </TouchableOpacity>
                </View>
              </View>

              <View className="flex-row justify-between items-center">
                <Text className="text-gray-700">Subtono:</Text>
                <View className="flex-row items-center">
                  <Text className="font-medium mr-2 capitalize">{aiAnalysis.undertone}</Text>
                  <TouchableOpacity
                    onPress={() => {/* Open undertone selector */}}
                    className="bg-blue-100 p-1 rounded"
                  >
                    <Edit3 size={14} color="#3B82F6" />
                  </TouchableOpacity>
                </View>
              </View>

              <View className="flex-row justify-between items-center">
                <Text className="text-gray-700">Canas:</Text>
                <View className="flex-row items-center">
                  <Text className="font-medium mr-2">{aiAnalysis.grayPercentage}%</Text>
                  <TouchableOpacity
                    onPress={() => {/* Open percentage selector */}}
                    className="bg-blue-100 p-1 rounded"
                  >
                    <Edit3 size={14} color="#3B82F6" />
                  </TouchableOpacity>
                </View>
              </View>

              <View className="flex-row justify-between items-center">
                <Text className="text-gray-700">Condición:</Text>
                <View className="flex-row items-center">
                  <Text className="font-medium mr-2 capitalize">{aiAnalysis.condition}</Text>
                  <TouchableOpacity
                    onPress={() => {/* Open condition selector */}}
                    className="bg-blue-100 p-1 rounded"
                  >
                    <Edit3 size={14} color="#3B82F6" />
                  </TouchableOpacity>
                </View>
              </View>

              <View className="flex-row justify-between items-center">
                <Text className="text-gray-700">Porosidad:</Text>
                <View className="flex-row items-center">
                  <Text className="font-medium mr-2 capitalize">{aiAnalysis.porosity}</Text>
                  <TouchableOpacity
                    onPress={() => {/* Open porosity selector */}}
                    className="bg-blue-100 p-1 rounded"
                  >
                    <Edit3 size={14} color="#3B82F6" />
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>

          {/* Existing Color */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <Text className="text-lg font-semibold mb-2">Color Existente</Text>
            <Text className="text-gray-700 mb-2">{aiAnalysis.existingColor}</Text>
            
            {aiAnalysis.previousTreatments.length > 0 && (
              <View>
                <Text className="font-medium text-gray-700 mb-1">Tratamientos Previos:</Text>
                {aiAnalysis.previousTreatments.map((treatment, index) => (
                  <Text key={index} className="text-sm text-gray-600">
                    • {treatment}
                  </Text>
                ))}
              </View>
            )}
          </View>

          {/* Stylist Notes */}
          <View className="bg-white rounded-lg p-4 mb-6">
            <Text className="text-lg font-semibold mb-2">Notas del Estilista</Text>
            <Text className="text-sm text-gray-600 mb-2">
              Añade observaciones adicionales o correcciones al análisis IA:
            </Text>
            <TouchableOpacity className="border border-gray-300 rounded-lg p-3 bg-gray-50">
              <Text className="text-gray-500">Toca para añadir notas...</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>

        {/* Action Buttons */}
        <View className="p-4 bg-white border-t border-gray-200">
          <View className="flex-row space-x-3">
            <TouchableOpacity
              className="flex-1 bg-gray-200 py-3 px-4 rounded-lg"
              onPress={() => setShowValidation(false)}
            >
              <Text className="text-gray-700 font-semibold text-center">
                Volver a Fotos
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              className="flex-1 bg-green-500 py-3 px-4 rounded-lg flex-row items-center justify-center"
              onPress={handleFinalApproval}
            >
              <CheckCircle size={18} color="white" />
              <Text className="text-white font-semibold ml-2">
                Confirmar Análisis
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      <View className="p-4">
        <Text className="text-2xl font-bold text-gray-800 mb-2">
          Color Actual
        </Text>
        <Text className="text-gray-600">
          {client?.name} - Captura fotos para análisis IA del color actual
        </Text>
      </View>

      <ScrollView className="flex-1 px-4">
        {/* Photo Capture Guide */}
        <View className="bg-blue-50 p-4 rounded-lg mb-4">
          <View className="flex-row items-center mb-2">
            <Eye size={20} color="#3B82F6" />
            <Text className="ml-2 font-semibold text-blue-900">
              Guía de Captura Óptima
            </Text>
          </View>
          <Text className="text-blue-800 text-sm">
            • Usa luz natural siempre que sea posible{"\n"}
            • Evita sombras fuertes en el cabello{"\n"}
            • Captura al menos 3 zonas obligatorias{"\n"}
            • Asegúrate de que el cabello esté bien visible
          </Text>
        </View>

        {/* Photo Zones */}
        {REQUIRED_ZONES.map((zone) => {
          const zonePhotos = getPhotosForZone(zone.id);
          return (
            <View key={zone.id} className="bg-white rounded-lg p-4 mb-3">
              <View className="flex-row items-center justify-between mb-2">
                <View>
                  <Text className="font-medium">
                    {zone.name}
                    {zone.required && <Text className="text-red-500"> *</Text>}
                  </Text>
                  <Text className="text-sm text-gray-600">{zone.description}</Text>
                </View>
                <TouchableOpacity
                  onPress={() => showPhotoOptions(zone.id, zone.name)}
                  className="bg-blue-500 px-3 py-2 rounded-lg flex-row items-center"
                >
                  <Camera size={16} color="white" />
                  <Text className="text-white text-sm ml-1">Capturar</Text>
                </TouchableOpacity>
              </View>

              {zonePhotos.length > 0 && (
                <View className="flex-row flex-wrap mt-2">
                  {zonePhotos.map((photo) => (
                    <View key={photo.id} className="w-20 h-20 mr-2 mb-2 relative">
                      <Image
                        source={{ uri: photo.uri }}
                        className="w-full h-full rounded-lg"
                        resizeMode="cover"
                      />
                      <View className="absolute top-1 right-1">
                        <TouchableOpacity
                          onPress={() => removePhoto(photo.id)}
                          className="bg-red-500 rounded-full p-1"
                        >
                          <Text className="text-white text-xs">×</Text>
                        </TouchableOpacity>
                      </View>
                      <View className="absolute bottom-1 left-1">
                        <Text className={`text-xs font-medium ${getQualityColor(photo.quality)}`}>
                          {photo.quality.toUpperCase()}
                        </Text>
                      </View>
                    </View>
                  ))}
                </View>
              )}
            </View>
          );
        })}

        {/* Progress */}
        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="font-semibold mb-2">Progreso de Captura</Text>
          <Text className="text-sm text-gray-600 mb-2">
            {getRequiredPhotosCount()}/3 zonas obligatorias • {photos.length} fotos totales
          </Text>
          <View className="bg-gray-200 rounded-full h-2">
            <View
              className="bg-green-500 rounded-full h-2"
              style={{ width: `${(getRequiredPhotosCount() / 3) * 100}%` }}
            />
          </View>
        </View>
      </ScrollView>

      {/* Analyze Button */}
      <View className="p-4 bg-white border-t border-gray-200">
        <TouchableOpacity
          className={`py-3 px-4 rounded-lg flex-row items-center justify-center ${
            canAnalyze() ? "bg-blue-500" : "bg-gray-300"
          }`}
          onPress={handleAnalyzeWithAI}
          disabled={!canAnalyze()}
        >
          <Zap size={18} color="white" />
          <Text className="text-white font-semibold ml-2">
            {canAnalyze() 
              ? `Analizar ${photos.length} Fotos con IA`
              : `Captura ${3 - getRequiredPhotosCount()} fotos más`
            }
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default CurrentColorAnalysisPhase;
