import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
} from "react-native";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  CheckCircle,
  Edit3,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  DollarSign,
  Star,
  Settings,
} from "lucide-react-native";

interface FormulationData {
  formula: {
    baseColor: {
      brand: string;
      line: string;
      shade: string;
      volume: string;
    };
    developer: {
      brand: string;
      volume: string;
      amount: string;
    };
    additives: Array<{
      type: "toner" | "booster" | "protector" | "neutralizer";
      brand: string;
      product: string;
      amount: string;
    }>;
    mixingRatio: string;
    totalVolume: string;
  };
  process: {
    steps: Array<{
      order: number;
      action: string;
      duration: number;
      temperature?: string;
      notes?: string;
    }>;
    totalTime: number;
    difficulty: "beginner" | "intermediate" | "advanced";
    riskLevel: "low" | "medium" | "high";
  };
  viability: {
    feasible: boolean;
    confidence: number;
    warnings: string[];
    alternatives: string[];
    sessionsRequired: number;
  };
  cost: {
    productCost: number;
    timeValue: number;
    totalEstimate: number;
    profitMargin: number;
  };
  stylistApproval: {
    approved: boolean;
    modifications: Record<string, any>;
    notes: string;
  };
}

interface IntelligentFormulationPhaseProps {
  client: any;
  currentColorAnalysis: any;
  desiredColorAnalysis: any;
  stylistBrands: string[];
  onComplete: (formulation: FormulationData) => void;
  onBack?: () => void;
}

const BRAND_CATALOGS = {
  loreal: {
    name: "L'Oréal Professionnel",
    lines: {
      majirel: "Majirel",
      inoa: "INOA",
      dialight: "Dialight",
      blond_studio: "Blond Studio",
    },
    developers: ["10 vol", "20 vol", "30 vol", "40 vol"],
  },
  wella: {
    name: "Wella Professionals",
    lines: {
      koleston: "Koleston Perfect",
      illumina: "Illumina Color",
      color_touch: "Color Touch",
      blondor: "Blondor",
    },
    developers: ["6%", "9%", "12%"],
  },
  schwarzkopf: {
    name: "Schwarzkopf Professional",
    lines: {
      igora: "Igora Royal",
      blondme: "BlondMe",
      color10: "Color10",
    },
    developers: ["3%", "6%", "9%", "12%"],
  },
};

const IntelligentFormulationPhase: React.FC<IntelligentFormulationPhaseProps> = ({
  client,
  currentColorAnalysis,
  desiredColorAnalysis,
  stylistBrands,
  onComplete,
  onBack,
}) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);
  const [formulation, setFormulation] = useState<FormulationData | null>(null);
  const [showApproval, setShowApproval] = useState(false);
  const [stylistNotes, setStylistNotes] = useState("");

  const simulateIntelligentFormulation = async (): Promise<FormulationData> => {
    const steps = [
      "Analizando color actual vs. deseado...",
      "Consultando catálogos de marcas preferidas...",
      "Calculando formulación óptima...",
      "Evaluando viabilidad y riesgos...",
      "Generando proceso paso a paso...",
      "Calculando costos y tiempo...",
      "Finalizando recomendaciones...",
    ];

    for (let i = 0; i < steps.length; i++) {
      setGenerationProgress((i + 1) / steps.length * 100);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Get primary brand from stylist preferences
    const primaryBrand = stylistBrands[0] || "loreal";
    const brandInfo = BRAND_CATALOGS[primaryBrand as keyof typeof BRAND_CATALOGS];

    // Analyze color difference to determine process
    const currentLevel = currentColorAnalysis?.aiAnalysis?.naturalLevel || 4;
    const currentCondition = currentColorAnalysis?.aiAnalysis?.condition || "good";
    const desiredFeasibility = desiredColorAnalysis?.aiAnalysis?.feasibility || "high";
    const riskLevel = desiredColorAnalysis?.aiAnalysis?.riskLevel || "low";

    // Generate intelligent formulation
    const needsLightening = currentLevel < 6; // Assuming desired color is lighter
    const isHighRisk = currentCondition === "damaged" || riskLevel === "high";

    const mockFormulation: FormulationData = {
      formula: {
        baseColor: {
          brand: brandInfo.name,
          line: Object.values(brandInfo.lines)[0],
          shade: needsLightening ? "8.31 Rubio Claro Dorado Ceniza" : "6.3 Rubio Oscuro Dorado",
          volume: "60ml",
        },
        developer: {
          brand: brandInfo.name,
          volume: needsLightening ? brandInfo.developers[2] : brandInfo.developers[1],
          amount: "60ml",
        },
        additives: [
          ...(needsLightening ? [{
            type: "protector" as const,
            brand: brandInfo.name,
            product: "Smartbond Step 1",
            amount: "5ml",
          }] : []),
          {
            type: "toner" as const,
            brand: brandInfo.name,
            product: "Toner .31",
            amount: "10ml",
          },
        ],
        mixingRatio: "1:1",
        totalVolume: needsLightening ? "125ml" : "70ml",
      },
      process: {
        steps: [
          {
            order: 1,
            action: isHighRisk ? "Aplicar tratamiento protector" : "Preparar mezcla",
            duration: isHighRisk ? 10 : 5,
            notes: isHighRisk ? "Smartbond para protección" : "Mezclar hasta consistencia cremosa",
          },
          {
            order: 2,
            action: "Aplicar en raíces",
            duration: 25,
            temperature: "Temperatura ambiente",
            notes: "Comenzar por zona posterior",
          },
          {
            order: 3,
            action: "Aplicar en medios y puntas",
            duration: 15,
            notes: "Menor tiempo de exposición",
          },
          {
            order: 4,
            action: "Enjuagar y aplicar toner",
            duration: 20,
            notes: "Toner para neutralizar subtonos",
          },
        ],
        totalTime: isHighRisk ? 70 : 60,
        difficulty: needsLightening ? "intermediate" : "beginner",
        riskLevel: isHighRisk ? "high" : riskLevel as any,
      },
      viability: {
        feasible: desiredFeasibility !== "low",
        confidence: 91,
        warnings: [
          ...(isHighRisk ? ["Cabello en condición delicada - proceder con precaución"] : []),
          ...(needsLightening ? ["Requiere decoloración - evaluar elasticidad"] : []),
        ],
        alternatives: [
          "Proceso gradual en 2 sesiones",
          "Técnica balayage para menor impacto",
        ],
        sessionsRequired: isHighRisk ? 2 : 1,
      },
      cost: {
        productCost: needsLightening ? 28 : 18,
        timeValue: 45,
        totalEstimate: needsLightening ? 73 : 63,
        profitMargin: 65,
      },
      stylistApproval: {
        approved: false,
        modifications: {},
        notes: "",
      },
    };

    return mockFormulation;
  };

  const handleGenerateFormulation = async () => {
    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      const generatedFormulation = await simulateIntelligentFormulation();
      setFormulation(generatedFormulation);
      setShowApproval(true);
    } catch (error) {
      console.error("Error generating formulation:", error);
      Alert.alert("Error", "No se pudo generar la formulación inteligente.");
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  };

  const handleApproveFormulation = () => {
    if (!formulation) return;

    const finalFormulation: FormulationData = {
      ...formulation,
      stylistApproval: {
        approved: true,
        modifications: {},
        notes: stylistNotes,
      },
    };

    onComplete(finalFormulation);
  };

  const handleModifyFormulation = () => {
    Alert.alert(
      "Modificar Formulación",
      "¿Qué aspecto quieres modificar?",
      [
        { text: "Productos", onPress: () => {/* Open product selector */} },
        { text: "Proceso", onPress: () => {/* Open process editor */} },
        { text: "Tiempos", onPress: () => {/* Open timing editor */} },
        { text: "Cancelar", style: "cancel" },
      ]
    );
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "beginner": return "text-green-600";
      case "intermediate": return "text-yellow-600";
      case "advanced": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "low": return "text-green-600";
      case "medium": return "text-yellow-600";
      case "high": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  if (isGenerating) {
    return (
      <View className="flex-1 bg-gray-50 justify-center items-center">
        <Beaker size={48} color="#F59E0B" className="mb-4" />
        <Text className="text-lg font-semibold text-gray-800 mb-2">
          Generando Formulación Inteligente
        </Text>
        <Text className="text-gray-600 text-center px-8 mb-4">
          Analizando {stylistBrands.length} marcas preferidas y creando la fórmula perfecta...
        </Text>
        <View className="w-64 bg-gray-200 rounded-full h-2 mb-2">
          <View
            className="bg-orange-500 rounded-full h-2 transition-all duration-300"
            style={{ width: `${generationProgress}%` }}
          />
        </View>
        <Text className="text-sm text-gray-500">
          {Math.round(generationProgress)}% completado
        </Text>
      </View>
    );
  }

  if (showApproval && formulation) {
    return (
      <View className="flex-1 bg-gray-50">
        <View className="p-4">
          <Text className="text-2xl font-bold text-gray-800 mb-2">
            Formulación Inteligente
          </Text>
          <Text className="text-gray-600">
            {client?.name} - Revisa y aprueba la fórmula generada por IA
          </Text>
        </View>

        <ScrollView className="flex-1 px-4">
          {/* Viability Overview */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <View className="flex-row items-center justify-between mb-3">
              <Text className="text-lg font-semibold">Viabilidad del Proceso</Text>
              <View className={`px-3 py-1 rounded-full ${
                formulation.viability.feasible ? "bg-green-100" : "bg-red-100"
              }`}>
                <Text className={`text-sm font-medium ${
                  formulation.viability.feasible ? "text-green-800" : "text-red-800"
                }`}>
                  {formulation.viability.feasible ? "VIABLE" : "NO VIABLE"}
                </Text>
              </View>
            </View>

            <View className="space-y-2">
              <View className="flex-row justify-between">
                <Text className="text-gray-700">Confianza IA:</Text>
                <Text className="font-medium">{formulation.viability.confidence}%</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-700">Sesiones:</Text>
                <Text className="font-medium">{formulation.viability.sessionsRequired}</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-700">Dificultad:</Text>
                <Text className={`font-medium capitalize ${getDifficultyColor(formulation.process.difficulty)}`}>
                  {formulation.process.difficulty}
                </Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-700">Riesgo:</Text>
                <Text className={`font-medium capitalize ${getRiskColor(formulation.process.riskLevel)}`}>
                  {formulation.process.riskLevel}
                </Text>
              </View>
            </View>
          </View>

          {/* Formula Details */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <View className="flex-row items-center justify-between mb-3">
              <Text className="text-lg font-semibold">Fórmula</Text>
              <TouchableOpacity
                onPress={handleModifyFormulation}
                className="bg-blue-100 p-2 rounded-lg"
              >
                <Edit3 size={16} color="#3B82F6" />
              </TouchableOpacity>
            </View>

            <View className="space-y-3">
              {/* Base Color */}
              <View className="border-l-4 border-orange-500 pl-3">
                <Text className="font-medium text-gray-800">Color Base</Text>
                <Text className="text-gray-600">{formulation.formula.baseColor.brand}</Text>
                <Text className="text-gray-600">{formulation.formula.baseColor.line}</Text>
                <Text className="font-medium">{formulation.formula.baseColor.shade}</Text>
                <Text className="text-sm text-gray-500">{formulation.formula.baseColor.volume}</Text>
              </View>

              {/* Developer */}
              <View className="border-l-4 border-blue-500 pl-3">
                <Text className="font-medium text-gray-800">Oxidante</Text>
                <Text className="text-gray-600">{formulation.formula.developer.brand}</Text>
                <Text className="font-medium">{formulation.formula.developer.volume}</Text>
                <Text className="text-sm text-gray-500">{formulation.formula.developer.amount}</Text>
              </View>

              {/* Additives */}
              {formulation.formula.additives.map((additive, index) => (
                <View key={index} className="border-l-4 border-purple-500 pl-3">
                  <Text className="font-medium text-gray-800 capitalize">{additive.type}</Text>
                  <Text className="text-gray-600">{additive.brand}</Text>
                  <Text className="font-medium">{additive.product}</Text>
                  <Text className="text-sm text-gray-500">{additive.amount}</Text>
                </View>
              ))}

              {/* Mixing Info */}
              <View className="bg-gray-50 p-3 rounded-lg">
                <Text className="font-medium text-gray-800">Mezcla</Text>
                <Text className="text-gray-600">Proporción: {formulation.formula.mixingRatio}</Text>
                <Text className="text-gray-600">Volumen total: {formulation.formula.totalVolume}</Text>
              </View>
            </div>
          </View>

          {/* Process Steps */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <Text className="text-lg font-semibold mb-3">Proceso Paso a Paso</Text>
            
            <View className="space-y-3">
              {formulation.process.steps.map((step) => (
                <View key={step.order} className="flex-row">
                  <View className="w-8 h-8 bg-orange-500 rounded-full items-center justify-center mr-3">
                    <Text className="text-white font-bold text-sm">{step.order}</Text>
                  </View>
                  <View className="flex-1">
                    <Text className="font-medium text-gray-800">{step.action}</Text>
                    <View className="flex-row items-center mt-1">
                      <Clock size={14} color="#6B7280" />
                      <Text className="text-sm text-gray-600 ml-1">{step.duration} min</Text>
                      {step.temperature && (
                        <Text className="text-sm text-gray-600 ml-3">{step.temperature}</Text>
                      )}
                    </View>
                    {step.notes && (
                      <Text className="text-sm text-gray-500 mt-1">{step.notes}</Text>
                    )}
                  </View>
                </View>
              ))}
            </View>

            <View className="mt-4 p-3 bg-orange-50 rounded-lg">
              <Text className="font-medium text-orange-800">
                Tiempo Total: {formulation.process.totalTime} minutos
              </Text>
            </View>
          </View>

          {/* Cost Breakdown */}
          <View className="bg-white rounded-lg p-4 mb-4">
            <Text className="text-lg font-semibold mb-3">Análisis de Costos</Text>
            
            <View className="space-y-2">
              <View className="flex-row justify-between">
                <Text className="text-gray-700">Productos:</Text>
                <Text className="font-medium">€{formulation.cost.productCost}</Text>
              </View>
              <View className="flex-row justify-between">
                <Text className="text-gray-700">Tiempo (valor):</Text>
                <Text className="font-medium">€{formulation.cost.timeValue}</Text>
              </View>
              <View className="border-t border-gray-200 pt-2">
                <View className="flex-row justify-between">
                  <Text className="font-semibold text-gray-800">Total Estimado:</Text>
                  <Text className="font-bold text-lg">€{formulation.cost.totalEstimate}</Text>
                </View>
              </View>
              <Text className="text-sm text-gray-500">
                Margen de beneficio: {formulation.cost.profitMargin}%
              </Text>
            </View>
          </View>

          {/* Warnings */}
          {formulation.viability.warnings.length > 0 && (
            <View className="bg-yellow-50 rounded-lg p-4 mb-4">
              <View className="flex-row items-center mb-2">
                <AlertTriangle size={20} color="#F59E0B" />
                <Text className="ml-2 font-semibold text-yellow-800">Advertencias</Text>
              </View>
              {formulation.viability.warnings.map((warning, index) => (
                <Text key={index} className="text-yellow-700 text-sm mb-1">
                  • {warning}
                </Text>
              ))}
            </View>
          )}

          {/* Stylist Notes */}
          <View className="bg-white rounded-lg p-4 mb-6">
            <Text className="text-lg font-semibold mb-2">Notas del Estilista</Text>
            <Text className="text-sm text-gray-600 mb-2">
              Observaciones sobre la formulación:
            </Text>
            <TouchableOpacity className="border border-gray-300 rounded-lg p-3 bg-gray-50">
              <Text className="text-gray-500">Toca para añadir notas...</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>

        {/* Action Buttons */}
        <View className="p-4 bg-white border-t border-gray-200">
          <View className="flex-row space-x-3">
            <TouchableOpacity
              className="flex-1 bg-gray-200 py-3 px-4 rounded-lg"
              onPress={() => setShowApproval(false)}
            >
              <Text className="text-gray-700 font-semibold text-center">
                Regenerar
              </Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              className="flex-1 bg-orange-500 py-3 px-4 rounded-lg flex-row items-center justify-center"
              onPress={handleApproveFormulation}
            >
              <CheckCircle size={18} color="white" />
              <Text className="text-white font-semibold ml-2">
                Aprobar Fórmula
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-50">
      <View className="p-4">
        <Text className="text-2xl font-bold text-gray-800 mb-2">
          Formulación Inteligente
        </Text>
        <Text className="text-gray-600">
          {client?.name} - IA creará la fórmula perfecta con tus marcas preferidas
        </Text>
      </View>

      <ScrollView className="flex-1 px-4">
        {/* Stylist Brands */}
        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="text-lg font-semibold mb-3">Marcas Configuradas</Text>
          <View className="flex-row flex-wrap">
            {stylistBrands.map((brandKey) => {
              const brand = BRAND_CATALOGS[brandKey as keyof typeof BRAND_CATALOGS];
              return (
                <View key={brandKey} className="bg-blue-100 px-3 py-2 rounded-lg mr-2 mb-2">
                  <Text className="text-blue-800 font-medium">{brand?.name || brandKey}</Text>
                </View>
              );
            })}
          </View>
          <TouchableOpacity className="mt-3 flex-row items-center">
            <Settings size={16} color="#6B7280" />
            <Text className="text-gray-600 ml-2 text-sm">Configurar marcas en ajustes</Text>
          </TouchableOpacity>
        </View>

        {/* Analysis Summary */}
        <View className="bg-white rounded-lg p-4 mb-4">
          <Text className="text-lg font-semibold mb-3">Resumen del Análisis</Text>
          
          <View className="space-y-3">
            <View>
              <Text className="font-medium text-gray-800">Color Actual</Text>
              <Text className="text-gray-600">
                Nivel {currentColorAnalysis?.aiAnalysis?.naturalLevel} • 
                {currentColorAnalysis?.aiAnalysis?.undertone} • 
                {currentColorAnalysis?.aiAnalysis?.grayPercentage}% canas
              </Text>
            </View>

            <View>
              <Text className="font-medium text-gray-800">Color Deseado</Text>
              <Text className="text-gray-600">
                {desiredColorAnalysis?.colorPalette?.primary} • 
                Técnica: {desiredColorAnalysis?.colorPalette?.technique}
              </Text>
            </View>

            <View>
              <Text className="font-medium text-gray-800">Viabilidad IA</Text>
              <Text className={`capitalize ${
                desiredColorAnalysis?.aiAnalysis?.feasibility === "high" ? "text-green-600" :
                desiredColorAnalysis?.aiAnalysis?.feasibility === "medium" ? "text-yellow-600" :
                "text-red-600"
              }`}>
                {desiredColorAnalysis?.aiAnalysis?.feasibility} • 
                {desiredColorAnalysis?.aiAnalysis?.sessionsRequired} sesión(es)
              </Text>
            </View>
          </View>
        </View>

        {/* AI Capabilities */}
        <View className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg p-4 mb-6">
          <View className="flex-row items-center mb-3">
            <Zap size={24} color="#F59E0B" />
            <Text className="ml-2 text-lg font-semibold text-orange-800">
              IA Formulación Avanzada
            </Text>
          </View>
          
          <Text className="text-orange-700 text-sm mb-3">
            La IA analizará todos los datos y generará:
          </Text>
          
          <View className="space-y-1">
            <Text className="text-orange-700 text-sm">• Fórmula exacta con tus marcas</Text>
            <Text className="text-orange-700 text-sm">• Proceso paso a paso optimizado</Text>
            <Text className="text-orange-700 text-sm">• Análisis de viabilidad y riesgos</Text>
            <Text className="text-orange-700 text-sm">• Cálculo de costos y tiempo</Text>
            <Text className="text-orange-700 text-sm">• Recomendaciones profesionales</Text>
          </View>
        </View>
      </ScrollView>

      {/* Generate Button */}
      <View className="p-4 bg-white border-t border-gray-200">
        <TouchableOpacity
          className="bg-orange-500 py-3 px-4 rounded-lg flex-row items-center justify-center"
          onPress={handleGenerateFormulation}
        >
          <Beaker size={18} color="white" />
          <Text className="text-white font-semibold ml-2">
            Generar Formulación Inteligente
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default IntelligentFormulationPhase;
