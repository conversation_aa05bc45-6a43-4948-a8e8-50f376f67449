import React, { useState, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  ScrollView,
  Alert,
  Dimensions,
  TextInput,
} from "react-native";
import {
  Search,
  User,
  Shield,
  FileText,
  CheckCircle,
  X,
  Edit3,
  Mail,
  Clock,
  UserPlus,
} from "lucide-react-native";
// import { Canvas, Path, Skia } from "@shopify/react-native-skia";
import ClientSearchBar from "./ClientSearchBar";

interface Client {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  avatar: string;
  lastService: string;
  lastVisit: string;
  isFrequent: boolean;
  totalVisits: number;
  preferredServices: string[];
  notes?: string;
}

interface ConsentData {
  imageCapture: boolean;
  dataAnalysis: boolean;
  aiProcessing: boolean;
  dataStorage: boolean;
  signature: string;
  timestamp: string;
  stylistWitness: string;
  clientName: string;
  clientId: string;
}

interface ClientSelectionWithConsentProps {
  onClientSelected: (client: Client, consent: ConsentData) => void;
  onBack?: () => void;
  stylistName: string;
}

const { width: screenWidth, height: screenHeight } = Dimensions.get("window");

const ClientSelectionWithConsent: React.FC<ClientSelectionWithConsentProps> = ({
  onClientSelected,
  onBack,
  stylistName = "Estilista",
}) => {
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [showConsentModal, setShowConsentModal] = useState(false);
  const [showGuestForm, setShowGuestForm] = useState(false);
  const [guestName, setGuestName] = useState("");
  const [guestPhone, setGuestPhone] = useState("");
  const [guestEmail, setGuestEmail] = useState("");
  
  // Consent states
  const [consent, setConsent] = useState({
    imageCapture: false,
    dataAnalysis: false,
    aiProcessing: false,
    dataStorage: false,
  });
  
  // Signature states
  const [signatureConfirmed, setSignatureConfirmed] = useState(false);

  const handleClientSelect = (client: Client) => {
    setSelectedClient(client);
    setShowConsentModal(true);
  };

  const handleCreateGuest = (name: string) => {
    setGuestName(name);
    setShowGuestForm(true);
  };

  const createGuestClient = () => {
    if (!guestName.trim()) {
      Alert.alert("Error", "El nombre del cliente es obligatorio");
      return;
    }

    const guestClient: Client = {
      id: `guest_${Date.now()}`,
      name: guestName.trim(),
      phone: guestPhone.trim() || undefined,
      email: guestEmail.trim() || undefined,
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${guestName}`,
      lastService: "Primera visita",
      lastVisit: "Hoy",
      isFrequent: false,
      totalVisits: 0,
      preferredServices: [],
      notes: "Cliente invitado - Primera consulta",
    };

    setSelectedClient(guestClient);
    setShowGuestForm(false);
    setShowConsentModal(true);
  };

  const handleConsentChange = (key: keyof typeof consent) => {
    setConsent(prev => ({ ...prev, [key]: !prev[key] }));
  };

  const allRequiredConsentsGiven = () => {
    return consent.imageCapture && consent.dataAnalysis && consent.aiProcessing;
  };

  const handleSignatureConfirm = () => {
    setSignatureConfirmed(true);
  };

  const clearSignature = () => {
    setSignatureConfirmed(false);
  };

  const simulateEmailConfirmation = async (client: Client, consentData: ConsentData) => {
    // Simulate email sending
    return new Promise((resolve) => {
      setTimeout(() => {
        Alert.alert(
          "Email Enviado",
          `Se ha enviado una confirmación de consentimiento a ${client.email || "el cliente"}.`,
          [{ text: "OK", onPress: () => resolve(true) }]
        );
      }, 1000);
    });
  };

  const handleConfirmConsent = async () => {
    if (!selectedClient) return;

    if (!allRequiredConsentsGiven()) {
      Alert.alert(
        "Consentimientos Requeridos",
        "Los consentimientos para captura de imágenes, análisis de datos y procesamiento con IA son obligatorios."
      );
      return;
    }

    if (!signatureConfirmed) {
      Alert.alert("Firma Requerida", "La confirmación de firma del cliente es obligatoria.");
      return;
    }

    const consentData: ConsentData = {
      ...consent,
      signature: "digital_signature_confirmed",
      timestamp: new Date().toISOString(),
      stylistWitness: stylistName,
      clientName: selectedClient.name,
      clientId: selectedClient.id,
    };

    try {
      // Simulate email confirmation
      await simulateEmailConfirmation(selectedClient, consentData);
      
      // Close modal and proceed
      setShowConsentModal(false);
      onClientSelected(selectedClient, consentData);
    } catch (error) {
      Alert.alert("Error", "No se pudo procesar el consentimiento. Inténtalo de nuevo.");
    }
  };

  const renderConsentModal = () => (
    <Modal
      visible={showConsentModal}
      animationType="slide"
      presentationStyle="pageSheet"
    >
      <View className="flex-1 bg-white">
        <View className="flex-row items-center justify-between p-4 border-b border-gray-200">
          <TouchableOpacity onPress={() => setShowConsentModal(false)}>
            <X size={24} color="#666" />
          </TouchableOpacity>
          <Text className="text-lg font-semibold">Consentimiento Informado</Text>
          <View style={{ width: 24 }} />
        </View>

        <ScrollView className="flex-1 p-4">
          {selectedClient && (
            <View className="bg-blue-50 p-4 rounded-lg mb-6">
              <Text className="text-lg font-semibold text-blue-900">
                {selectedClient.name}
              </Text>
              <Text className="text-blue-700">
                {selectedClient.phone && `Tel: ${selectedClient.phone}`}
              </Text>
              <Text className="text-blue-700">
                {selectedClient.email && `Email: ${selectedClient.email}`}
              </Text>
            </View>
          )}

          <Text className="text-base text-gray-800 mb-4">
            Para realizar la consulta de coloración asistida por IA, necesitamos su consentimiento 
            para los siguientes procesos:
          </Text>

          {/* Consent Categories */}
          <View className="space-y-4 mb-6">
            <TouchableOpacity
              className={`p-4 rounded-lg border-2 ${
                consent.imageCapture ? "border-green-500 bg-green-50" : "border-gray-300"
              }`}
              onPress={() => handleConsentChange("imageCapture")}
            >
              <View className="flex-row items-center">
                <CheckCircle 
                  size={20} 
                  color={consent.imageCapture ? "#10B981" : "#9CA3AF"} 
                />
                <Text className="ml-3 font-semibold">Captura de Imágenes *</Text>
              </View>
              <Text className="text-sm text-gray-600 mt-2">
                Autorizo la toma de fotografías de mi cabello para análisis profesional.
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`p-4 rounded-lg border-2 ${
                consent.dataAnalysis ? "border-green-500 bg-green-50" : "border-gray-300"
              }`}
              onPress={() => handleConsentChange("dataAnalysis")}
            >
              <View className="flex-row items-center">
                <CheckCircle 
                  size={20} 
                  color={consent.dataAnalysis ? "#10B981" : "#9CA3AF"} 
                />
                <Text className="ml-3 font-semibold">Análisis de Datos *</Text>
              </View>
              <Text className="text-sm text-gray-600 mt-2">
                Autorizo el análisis de mis datos capilares para diagnóstico profesional.
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`p-4 rounded-lg border-2 ${
                consent.aiProcessing ? "border-green-500 bg-green-50" : "border-gray-300"
              }`}
              onPress={() => handleConsentChange("aiProcessing")}
            >
              <View className="flex-row items-center">
                <CheckCircle 
                  size={20} 
                  color={consent.aiProcessing ? "#10B981" : "#9CA3AF"} 
                />
                <Text className="ml-3 font-semibold">Procesamiento con IA *</Text>
              </View>
              <Text className="text-sm text-gray-600 mt-2">
                Autorizo el uso de inteligencia artificial para análisis y recomendaciones.
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className={`p-4 rounded-lg border-2 ${
                consent.dataStorage ? "border-green-500 bg-green-50" : "border-gray-300"
              }`}
              onPress={() => handleConsentChange("dataStorage")}
            >
              <View className="flex-row items-center">
                <CheckCircle 
                  size={20} 
                  color={consent.dataStorage ? "#10B981" : "#9CA3AF"} 
                />
                <Text className="ml-3 font-semibold">Almacenamiento de Datos</Text>
              </View>
              <Text className="text-sm text-gray-600 mt-2">
                Autorizo el almacenamiento seguro de mis datos para futuras consultas.
              </Text>
            </TouchableOpacity>
          </View>

          {/* Signature Section */}
          <View className="mb-6">
            <Text className="text-lg font-semibold mb-2">Confirmación Digital *</Text>
            <Text className="text-sm text-gray-600 mb-4">
              Por favor, confirme que acepta los términos y condiciones:
            </Text>

            <TouchableOpacity
              className={`border-2 rounded-lg p-4 ${
                signatureConfirmed ? "border-green-500 bg-green-50" : "border-gray-300 bg-gray-50"
              }`}
              onPress={handleSignatureConfirm}
              style={{ height: 150 }}
            >
              <View className="flex-1 justify-center items-center">
                {signatureConfirmed ? (
                  <View className="items-center">
                    <CheckCircle size={48} color="#10B981" />
                    <Text className="text-green-700 font-semibold mt-2">
                      Confirmación Digital Realizada
                    </Text>
                    <Text className="text-green-600 text-sm">
                      {new Date().toLocaleString()}
                    </Text>
                  </View>
                ) : (
                  <View className="items-center">
                    <Edit3 size={48} color="#6B7280" />
                    <Text className="text-gray-600 font-medium mt-2">
                      Toque para Confirmar
                    </Text>
                    <Text className="text-gray-500 text-sm">
                      Confirmación digital requerida
                    </Text>
                  </View>
                )}
              </View>
            </TouchableOpacity>

            {signatureConfirmed && (
              <TouchableOpacity
                className="mt-2 p-2 bg-gray-200 rounded-lg"
                onPress={clearSignature}
              >
                <Text className="text-center text-gray-700">Limpiar Confirmación</Text>
              </TouchableOpacity>
            )}
          </View>

          <Text className="text-xs text-gray-500 mb-6">
            * Campos obligatorios. Al firmar, confirma que ha leído y acepta los términos.
            Testigo: {stylistName} - {new Date().toLocaleDateString()}
          </Text>
        </ScrollView>

        <View className="p-4 border-t border-gray-200">
          <TouchableOpacity
            className={`py-3 px-4 rounded-lg ${
              allRequiredConsentsGiven() && signatureConfirmed
                ? "bg-green-500"
                : "bg-gray-300"
            }`}
            onPress={handleConfirmConsent}
            disabled={!allRequiredConsentsGiven() || !signatureConfirmed}
          >
            <Text className="text-white font-semibold text-center">
              Confirmar Consentimiento y Continuar
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  return (
    <View className="flex-1 bg-gray-50">
      <View className="p-4">
        <View className="flex-row items-center mb-6">
          {onBack && (
            <TouchableOpacity onPress={onBack} className="mr-4">
              <X size={24} color="#666" />
            </TouchableOpacity>
          )}
          <View>
            <Text className="text-2xl font-bold text-gray-800">
              Seleccionar Cliente
            </Text>
            <Text className="text-gray-600">
              Busca un cliente existente o crea uno nuevo
            </Text>
          </View>
        </View>

        <ClientSearchBar
          onSelect={handleClientSelect}
          onCreateNew={handleCreateGuest}
          placeholder="Buscar por nombre, teléfono o email..."
          showFrequentFirst={true}
          maxResults={8}
        />

        <View className="mt-6 p-4 bg-blue-50 rounded-lg">
          <View className="flex-row items-center mb-2">
            <Shield size={20} color="#3B82F6" />
            <Text className="ml-2 font-semibold text-blue-900">
              Privacidad y Consentimiento
            </Text>
          </View>
          <Text className="text-sm text-blue-800">
            Antes de iniciar la consulta, solicitaremos el consentimiento informado 
            del cliente para la captura de imágenes y análisis con IA.
          </Text>
        </View>
      </View>

      {renderConsentModal()}
      {renderGuestForm()}
    </View>
  );

  function renderGuestForm() {
    return (
      <Modal
        visible={showGuestForm}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View className="flex-1 bg-white">
          <View className="flex-row items-center justify-between p-4 border-b border-gray-200">
            <TouchableOpacity onPress={() => setShowGuestForm(false)}>
              <X size={24} color="#666" />
            </TouchableOpacity>
            <Text className="text-lg font-semibold">Cliente Invitado</Text>
            <View style={{ width: 24 }} />
          </View>

          <ScrollView className="flex-1 p-4">
            <View className="mb-6">
              <View className="flex-row items-center mb-4">
                <UserPlus size={24} color="#3B82F6" />
                <Text className="ml-2 text-xl font-semibold text-gray-800">
                  Crear Cliente Invitado
                </Text>
              </View>
              <Text className="text-gray-600">
                Para consultas rápidas sin crear un perfil completo.
                Podrás convertirlo en cliente regular después del servicio.
              </Text>
            </View>

            <View className="space-y-4">
              <View>
                <Text className="text-sm font-medium text-gray-700 mb-2">
                  Nombre Completo *
                </Text>
                <TextInput
                  className="border border-gray-300 rounded-lg p-3 bg-white"
                  placeholder="Ej: María García"
                  value={guestName}
                  onChangeText={setGuestName}
                  autoCapitalize="words"
                />
              </View>

              <View>
                <Text className="text-sm font-medium text-gray-700 mb-2">
                  Teléfono (Opcional)
                </Text>
                <TextInput
                  className="border border-gray-300 rounded-lg p-3 bg-white"
                  placeholder="Ej: +34 666 123 456"
                  value={guestPhone}
                  onChangeText={setGuestPhone}
                  keyboardType="phone-pad"
                />
              </View>

              <View>
                <Text className="text-sm font-medium text-gray-700 mb-2">
                  Email (Opcional)
                </Text>
                <TextInput
                  className="border border-gray-300 rounded-lg p-3 bg-white"
                  placeholder="Ej: <EMAIL>"
                  value={guestEmail}
                  onChangeText={setGuestEmail}
                  keyboardType="email-address"
                  autoCapitalize="none"
                />
              </View>
            </View>

            <View className="mt-6 p-4 bg-yellow-50 rounded-lg">
              <Text className="text-sm text-yellow-800">
                <Text className="font-semibold">Nota:</Text> Los clientes invitados
                son ideales para consultas rápidas. Después del servicio, podrás
                convertir este perfil en un cliente regular con historial completo.
              </Text>
            </View>
          </ScrollView>

          <View className="p-4 border-t border-gray-200">
            <TouchableOpacity
              className={`py-3 px-4 rounded-lg ${
                guestName.trim() ? "bg-blue-500" : "bg-gray-300"
              }`}
              onPress={createGuestClient}
              disabled={!guestName.trim()}
            >
              <Text className="text-white font-semibold text-center">
                Crear Cliente Invitado
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  }
};

export default ClientSelectionWithConsent;
