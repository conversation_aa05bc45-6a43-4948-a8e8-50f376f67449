import React, { useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
  Alert,
  TextInput,
} from "react-native";
import {
  Camera,
  Star,
  Save,
  CheckCircle,
  Clock,
  FileText,
  Heart,
  Calendar,
  Upload,
  Trash2,
  Eye,
} from "lucide-react-native";
import * as ImagePicker from "expo-image-picker";
import * as FaceDetector from "expo-face-detector";
import * as ImageManipulator from "expo-image-manipulator";

interface ResultPhoto {
  id: string;
  uri: string;
  angle: "frontal" | "left" | "right" | "back" | "detail";
  timestamp: string;
  notes?: string;
}

interface AppliedFormula {
  originalFormula: any;
  actualFormula: any;
  modifications: string[];
  actualProcessingTime: number;
  actualSteps: string[];
  complications?: string[];
}

interface ClientSatisfaction {
  rating: number;
  comments: string;
  wouldRecommend: boolean;
  favoriteAspect?: string;
  improvementSuggestions?: string;
}

interface ServiceDocumentationData {
  resultPhotos: ResultPhoto[];
  appliedFormula: AppliedFormula;
  stylistNotes: string;
  clientSatisfaction?: ClientSatisfaction;
  maintenanceInstructions: string[];
  productRecommendations: string[];
  nextAppointment?: {
    suggestedDate: string;
    purpose: string;
    notes: string;
  };
  totalServiceTime: number;
  finalCost: number;
  timestamp: string;
}

interface ServiceDocumentationProps {
  originalFormula: any;
  onDocumentationComplete: (documentation: ServiceDocumentationData) => void;
  onBack?: () => void;
  clientName: string;
  stylistName: string;
}

const REQUIRED_ANGLES = [
  { id: "frontal", name: "Frontal", description: "Vista frontal del resultado", required: true },
  { id: "left", name: "Lateral Izquierdo", description: "Vista lateral izquierda", required: true },
  { id: "right", name: "Lateral Derecho", description: "Vista lateral derecha", required: false },
  { id: "back", name: "Posterior", description: "Vista posterior del cabello", required: true },
  { id: "detail", name: "Detalle", description: "Detalle específico del color/técnica", required: false },
];

const MAINTENANCE_TEMPLATES = [
  "Usar champú sin sulfatos específico para cabello teñido",
  "Aplicar mascarilla hidratante 2 veces por semana",
  "Evitar agua muy caliente al lavar el cabello",
  "Usar protector térmico antes del peinado con calor",
  "Proteger el cabello del sol con productos con UV",
  "Evitar piscinas con cloro durante las primeras 2 semanas",
];

const ServiceDocumentation: React.FC<ServiceDocumentationProps> = ({
  originalFormula,
  onDocumentationComplete,
  onBack,
  clientName,
  stylistName,
}) => {
  const [resultPhotos, setResultPhotos] = useState<ResultPhoto[]>([]);
  const [appliedFormula, setAppliedFormula] = useState<AppliedFormula>({
    originalFormula,
    actualFormula: originalFormula,
    modifications: [],
    actualProcessingTime: originalFormula.totalTime || 0,
    actualSteps: [],
  });
  const [stylistNotes, setStylistNotes] = useState("");
  const [clientSatisfaction, setClientSatisfaction] = useState<ClientSatisfaction>({
    rating: 5,
    comments: "",
    wouldRecommend: true,
  });
  const [maintenanceInstructions, setMaintenanceInstructions] = useState<string[]>(
    MAINTENANCE_TEMPLATES.slice(0, 4)
  );
  const [productRecommendations, setProductRecommendations] = useState<string[]>([]);
  const [nextAppointment, setNextAppointment] = useState({
    suggestedDate: "",
    purpose: "Retoque de raíces",
    notes: "",
  });
  const [totalServiceTime, setTotalServiceTime] = useState(0);
  const [finalCost, setFinalCost] = useState(originalFormula.estimatedCost || 0);
  const [showSatisfactionForm, setShowSatisfactionForm] = useState(false);

  const detectAndBlurFaces = async (imageUri: string): Promise<string> => {
    try {
      const faces = await FaceDetector.detectFacesAsync(imageUri, {
        mode: FaceDetector.FaceDetectorMode.fast,
        detectLandmarks: FaceDetector.FaceDetectorLandmarks.none,
        runClassifications: FaceDetector.FaceDetectorClassifications.none,
      });

      if (faces.length === 0) {
        return imageUri;
      }

      let manipulatedImage = imageUri;
      
      for (const face of faces) {
        const { bounds } = face;
        const padding = 20;
        const blurRegion = {
          originX: Math.max(0, bounds.origin.x - padding),
          originY: Math.max(0, bounds.origin.y - padding),
          width: bounds.size.width + (padding * 2),
          height: bounds.size.height + (padding * 2),
        };

        const result = await ImageManipulator.manipulateAsync(
          manipulatedImage,
          [{ crop: blurRegion }],
          {
            compress: 0.8,
            format: ImageManipulator.SaveFormat.JPEG,
          }
        );

        manipulatedImage = result.uri;
      }

      return manipulatedImage;
    } catch (error) {
      console.error("Error detecting/blurring faces:", error);
      return imageUri;
    }
  };

  const captureResultPhoto = async (angle: string) => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const processedUri = await detectAndBlurFaces(result.assets[0].uri);
        
        const newPhoto: ResultPhoto = {
          id: `photo_${Date.now()}`,
          uri: processedUri,
          angle: angle as any,
          timestamp: new Date().toISOString(),
        };

        setResultPhotos(prev => [...prev, newPhoto]);
      }
    } catch (error) {
      console.error("Error capturing photo:", error);
      Alert.alert("Error", "No se pudo capturar la foto.");
    }
  };

  const selectFromGallery = async (angle: string) => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const processedUri = await detectAndBlurFaces(result.assets[0].uri);
        
        const newPhoto: ResultPhoto = {
          id: `photo_${Date.now()}`,
          uri: processedUri,
          angle: angle as any,
          timestamp: new Date().toISOString(),
        };

        setResultPhotos(prev => [...prev, newPhoto]);
      }
    } catch (error) {
      console.error("Error selecting photo:", error);
      Alert.alert("Error", "No se pudo seleccionar la foto.");
    }
  };

  const removePhoto = (photoId: string) => {
    setResultPhotos(prev => prev.filter(photo => photo.id !== photoId));
  };

  const showPhotoOptions = (angle: string, angleName: string) => {
    Alert.alert(
      `Capturar ${angleName}`,
      "Elige cómo quieres añadir la foto del resultado",
      [
        { text: "Tomar Foto", onPress: () => captureResultPhoto(angle) },
        { text: "Elegir de Galería", onPress: () => selectFromGallery(angle) },
        { text: "Cancelar", style: "cancel" },
      ]
    );
  };

  const getPhotosForAngle = (angle: string) => {
    return resultPhotos.filter(photo => photo.angle === angle);
  };

  const getRequiredPhotosCount = () => {
    const requiredAngles = REQUIRED_ANGLES.filter(angle => angle.required);
    return requiredAngles.filter(angle => getPhotosForAngle(angle.id).length > 0).length;
  };

  const getTotalRequiredAngles = () => {
    return REQUIRED_ANGLES.filter(angle => angle.required).length;
  };

  const canComplete = () => {
    return getRequiredPhotosCount() >= getTotalRequiredAngles() && stylistNotes.trim().length > 0;
  };

  const handleComplete = () => {
    if (!canComplete()) {
      Alert.alert(
        "Documentación Incompleta",
        "Por favor, captura todas las fotos requeridas y añade notas del estilista."
      );
      return;
    }

    const documentation: ServiceDocumentationData = {
      resultPhotos,
      appliedFormula,
      stylistNotes,
      clientSatisfaction: showSatisfactionForm ? clientSatisfaction : undefined,
      maintenanceInstructions,
      productRecommendations,
      nextAppointment: nextAppointment.suggestedDate ? nextAppointment : undefined,
      totalServiceTime,
      finalCost,
      timestamp: new Date().toISOString(),
    };

    onDocumentationComplete(documentation);
  };

  const toggleMaintenanceInstruction = (instruction: string) => {
    setMaintenanceInstructions(prev =>
      prev.includes(instruction)
        ? prev.filter(i => i !== instruction)
        : [...prev, instruction]
    );
  };

  const renderStarRating = (rating: number, onRatingChange: (rating: number) => void) => {
    return (
      <View className="flex-row">
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity
            key={star}
            onPress={() => onRatingChange(star)}
            className="mr-1"
          >
            <Star
              size={24}
              color={star <= rating ? "#FFD700" : "#D1D5DB"}
              fill={star <= rating ? "#FFD700" : "transparent"}
            />
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  return (
    <View className="flex-1 bg-gray-50">
      <View className="p-4">
        <View className="flex-row items-center justify-between mb-4">
          <View>
            <Text className="text-2xl font-bold text-gray-800">
              Documentación del Servicio
            </Text>
            <Text className="text-gray-600">
              {clientName} - Registro completo del resultado
            </Text>
          </View>
          <View className="items-end">
            <Text className="text-sm font-medium text-green-700">
              {getRequiredPhotosCount()}/{getTotalRequiredAngles()} fotos requeridas
            </Text>
            <Text className="text-xs text-gray-500">
              {resultPhotos.length} fotos totales
            </Text>
          </View>
        </View>

        <View className="bg-blue-50 p-4 rounded-lg mb-4">
          <View className="flex-row items-center mb-2">
            <FileText size={20} color="#3B82F6" />
            <Text className="ml-2 font-semibold text-blue-900">
              Documentación Profesional
            </Text>
          </View>
          <Text className="text-blue-800 text-sm">
            Documenta el resultado final con fotos, fórmula aplicada, notas profesionales 
            y satisfacción del cliente para un historial completo.
          </Text>
        </View>
      </View>

      <ScrollView className="flex-1 px-4">
        {/* Result Photos */}
        <View className="mb-6">
          <Text className="text-lg font-semibold text-gray-800 mb-3">
            Fotos del Resultado
          </Text>
          
          {REQUIRED_ANGLES.map((angle) => {
            const photos = getPhotosForAngle(angle.id);
            return (
              <View key={angle.id} className="bg-white rounded-lg p-4 mb-3">
                <View className="flex-row items-center justify-between mb-2">
                  <View>
                    <Text className="font-medium">
                      {angle.name}
                      {angle.required && <Text className="text-red-500"> *</Text>}
                    </Text>
                    <Text className="text-sm text-gray-600">{angle.description}</Text>
                  </View>
                  <TouchableOpacity
                    onPress={() => showPhotoOptions(angle.id, angle.name)}
                    className="bg-blue-500 px-3 py-2 rounded-lg"
                  >
                    <Camera size={16} color="white" />
                  </TouchableOpacity>
                </View>

                {photos.length > 0 && (
                  <View className="flex-row flex-wrap mt-2">
                    {photos.map((photo) => (
                      <View key={photo.id} className="w-20 h-20 mr-2 mb-2 relative">
                        <Image
                          source={{ uri: photo.uri }}
                          className="w-full h-full rounded-lg"
                          resizeMode="cover"
                        />
                        <TouchableOpacity
                          onPress={() => removePhoto(photo.id)}
                          className="absolute -top-1 -right-1 bg-red-500 rounded-full p-1"
                        >
                          <Trash2 size={12} color="white" />
                        </TouchableOpacity>
                      </View>
                    ))}
                  </View>
                )}
              </View>
            );
          })}
        </View>

        {/* Applied Formula */}
        <View className="bg-white rounded-lg p-4 mb-6">
          <Text className="text-lg font-semibold mb-3">Fórmula Aplicada</Text>
          
          <View className="space-y-3">
            <View>
              <Text className="text-sm font-medium text-gray-700 mb-1">
                Tiempo Real de Procesamiento
              </Text>
              <TextInput
                className="border border-gray-300 rounded-lg p-3"
                placeholder="Minutos totales"
                value={appliedFormula.actualProcessingTime.toString()}
                onChangeText={(text) => {
                  const time = parseInt(text) || 0;
                  setAppliedFormula(prev => ({ ...prev, actualProcessingTime: time }));
                }}
                keyboardType="numeric"
              />
            </View>

            <View>
              <Text className="text-sm font-medium text-gray-700 mb-1">
                Modificaciones Realizadas
              </Text>
              <TextInput
                className="border border-gray-300 rounded-lg p-3"
                placeholder="Describe cualquier cambio realizado a la fórmula original..."
                value={appliedFormula.modifications.join(", ")}
                onChangeText={(text) => {
                  const modifications = text.split(",").map(m => m.trim()).filter(m => m);
                  setAppliedFormula(prev => ({ ...prev, modifications }));
                }}
                multiline
                numberOfLines={2}
              />
            </View>
          </View>
        </View>

        {/* Stylist Notes */}
        <View className="bg-white rounded-lg p-4 mb-6">
          <Text className="text-lg font-semibold mb-3">Notas del Estilista</Text>
          <TextInput
            className="border border-gray-300 rounded-lg p-3"
            placeholder="Observaciones del proceso, resultado obtenido, reacciones del cliente, recomendaciones..."
            value={stylistNotes}
            onChangeText={setStylistNotes}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>

        {/* Client Satisfaction */}
        <View className="bg-white rounded-lg p-4 mb-6">
          <View className="flex-row items-center justify-between mb-3">
            <Text className="text-lg font-semibold">Satisfacción del Cliente</Text>
            <TouchableOpacity
              onPress={() => setShowSatisfactionForm(!showSatisfactionForm)}
              className={`px-3 py-1 rounded-lg ${
                showSatisfactionForm ? "bg-green-100" : "bg-gray-100"
              }`}
            >
              <Text className={`text-sm ${
                showSatisfactionForm ? "text-green-700" : "text-gray-700"
              }`}>
                {showSatisfactionForm ? "Ocultar" : "Evaluar"}
              </Text>
            </TouchableOpacity>
          </View>

          {showSatisfactionForm && (
            <View className="space-y-4">
              <View>
                <Text className="text-sm font-medium text-gray-700 mb-2">
                  Valoración General
                </Text>
                {renderStarRating(clientSatisfaction.rating, (rating) =>
                  setClientSatisfaction(prev => ({ ...prev, rating }))
                )}
              </View>

              <View>
                <Text className="text-sm font-medium text-gray-700 mb-1">
                  Comentarios del Cliente
                </Text>
                <TextInput
                  className="border border-gray-300 rounded-lg p-3"
                  placeholder="¿Qué opina el cliente del resultado?"
                  value={clientSatisfaction.comments}
                  onChangeText={(text) =>
                    setClientSatisfaction(prev => ({ ...prev, comments: text }))
                  }
                  multiline
                  numberOfLines={3}
                />
              </View>

              <View className="flex-row items-center">
                <TouchableOpacity
                  onPress={() =>
                    setClientSatisfaction(prev => ({ 
                      ...prev, 
                      wouldRecommend: !prev.wouldRecommend 
                    }))
                  }
                  className={`w-6 h-6 rounded border-2 mr-3 items-center justify-center ${
                    clientSatisfaction.wouldRecommend ? "border-green-500 bg-green-500" : "border-gray-300"
                  }`}
                >
                  {clientSatisfaction.wouldRecommend && (
                    <CheckCircle size={16} color="white" />
                  )}
                </TouchableOpacity>
                <Text className="text-gray-700">¿Recomendaría nuestros servicios?</Text>
              </View>
            </View>
          )}
        </View>

        {/* Maintenance Instructions */}
        <View className="bg-white rounded-lg p-4 mb-6">
          <Text className="text-lg font-semibold mb-3">Instrucciones de Mantenimiento</Text>
          
          {MAINTENANCE_TEMPLATES.map((instruction, index) => (
            <TouchableOpacity
              key={index}
              onPress={() => toggleMaintenanceInstruction(instruction)}
              className="flex-row items-center mb-2"
            >
              <View className={`w-5 h-5 rounded border-2 mr-3 items-center justify-center ${
                maintenanceInstructions.includes(instruction) 
                  ? "border-blue-500 bg-blue-500" 
                  : "border-gray-300"
              }`}>
                {maintenanceInstructions.includes(instruction) && (
                  <CheckCircle size={12} color="white" />
                )}
              </View>
              <Text className="text-gray-700 flex-1">{instruction}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Service Summary */}
        <View className="bg-white rounded-lg p-4 mb-6">
          <Text className="text-lg font-semibold mb-3">Resumen del Servicio</Text>
          
          <View className="space-y-3">
            <View className="flex-row justify-between">
              <Text className="text-gray-600">Tiempo Total:</Text>
              <View className="flex-row items-center">
                <TextInput
                  className="border border-gray-300 rounded px-2 py-1 w-16 text-center mr-2"
                  value={totalServiceTime.toString()}
                  onChangeText={(text) => setTotalServiceTime(parseInt(text) || 0)}
                  keyboardType="numeric"
                />
                <Text className="text-gray-600">min</Text>
              </View>
            </View>

            <View className="flex-row justify-between">
              <Text className="text-gray-600">Coste Final:</Text>
              <View className="flex-row items-center">
                <Text className="text-gray-600 mr-1">€</Text>
                <TextInput
                  className="border border-gray-300 rounded px-2 py-1 w-16 text-center"
                  value={finalCost.toString()}
                  onChangeText={(text) => setFinalCost(parseFloat(text) || 0)}
                  keyboardType="numeric"
                />
              </View>
            </View>
          </View>
        </View>

        {/* Next Appointment */}
        <View className="bg-white rounded-lg p-4 mb-6">
          <Text className="text-lg font-semibold mb-3">Próxima Cita Sugerida</Text>
          
          <View className="space-y-3">
            <View>
              <Text className="text-sm font-medium text-gray-700 mb-1">
                Fecha Sugerida
              </Text>
              <TextInput
                className="border border-gray-300 rounded-lg p-3"
                placeholder="Ej: En 6-8 semanas"
                value={nextAppointment.suggestedDate}
                onChangeText={(text) =>
                  setNextAppointment(prev => ({ ...prev, suggestedDate: text }))
                }
              />
            </View>

            <View>
              <Text className="text-sm font-medium text-gray-700 mb-1">
                Propósito
              </Text>
              <TextInput
                className="border border-gray-300 rounded-lg p-3"
                placeholder="Ej: Retoque de raíces, mantenimiento..."
                value={nextAppointment.purpose}
                onChangeText={(text) =>
                  setNextAppointment(prev => ({ ...prev, purpose: text }))
                }
              />
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Complete Button */}
      <View className="p-4 bg-white border-t border-gray-200">
        <TouchableOpacity
          className={`py-3 px-4 rounded-lg flex-row items-center justify-center ${
            canComplete() ? "bg-green-500" : "bg-gray-300"
          }`}
          onPress={handleComplete}
          disabled={!canComplete()}
        >
          <Save size={18} color="white" />
          <Text className="text-white font-semibold ml-2">
            {canComplete() 
              ? "Completar Documentación" 
              : `Faltan ${getTotalRequiredAngles() - getRequiredPhotosCount()} fotos y notas`
            }
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default ServiceDocumentation;
