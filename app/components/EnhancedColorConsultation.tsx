import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Al<PERSON>,
  BackHandler,
} from "react-native";
import {
  ArrowLeft,
  CheckCircle,
  Clock,
  User,
  Eye,
  Palette,
  Beaker,
  FileText,
} from "lucide-react-native";
import { useRouter } from "expo-router";
import AsyncStorage from "@react-native-async-storage/async-storage";

// Import all phase components
import ClientSelectionWithConsent from "./ClientSelectionWithConsent";
import EnhancedHairDiagnosis from "./EnhancedHairDiagnosis";
import ChemicalHistoryExtractor from "./ChemicalHistoryExtractor";
import DiagnosisValidation from "./DiagnosisValidation";
import DesiredColorDefinition from "./DesiredColorDefinition";
import ExpertFormulation from "./ExpertFormulation";
import ServiceDocumentation from "./ServiceDocumentation";

interface ConsultationState {
  currentPhase: number;
  client: any;
  consentData: any;
  diagnosisData: any;
  chemicalHistory: any;
  validatedDiagnosis: any;
  desiredColor: any;
  formula: any;
  documentation: any;
  startTime: string;
  consultationId: string;
}

interface EnhancedColorConsultationProps {
  onComplete?: (consultationData: any) => void;
  onCancel?: () => void;
  initialClient?: any;
}

const PHASES = [
  { id: 1, name: "Selección de Cliente", icon: User, description: "Cliente y consentimiento" },
  { id: 2, name: "Diagnóstico Capilar", icon: Eye, description: "Análisis IA exhaustivo" },
  { id: 3, name: "Color Deseado", icon: Palette, description: "Definición precisa" },
  { id: 4, name: "Formulación", icon: Beaker, description: "Fórmula experta IA" },
  { id: 5, name: "Documentación", icon: FileText, description: "Registro completo" },
];

const EnhancedColorConsultation: React.FC<EnhancedColorConsultationProps> = ({
  onComplete = () => {},
  onCancel = () => {},
  initialClient,
}) => {
  const router = useRouter();
  const [consultationState, setConsultationState] = useState<ConsultationState>({
    currentPhase: 1,
    client: initialClient || null,
    consentData: null,
    diagnosisData: null,
    chemicalHistory: null,
    validatedDiagnosis: null,
    desiredColor: null,
    formula: null,
    documentation: null,
    startTime: new Date().toISOString(),
    consultationId: `consultation_${Date.now()}`,
  });

  useEffect(() => {
    // Load any existing consultation state
    loadConsultationState();

    // Handle Android back button
    const backHandler = BackHandler.addEventListener("hardwareBackPress", handleBackPress);
    return () => backHandler.remove();
  }, []);

  useEffect(() => {
    // Auto-save consultation state
    saveConsultationState();
  }, [consultationState]);

  const loadConsultationState = async () => {
    try {
      const savedState = await AsyncStorage.getItem(`consultation_${consultationState.consultationId}`);
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        setConsultationState(prev => ({ ...prev, ...parsedState }));
      }
    } catch (error) {
      console.error("Error loading consultation state:", error);
    }
  };

  const saveConsultationState = async () => {
    try {
      await AsyncStorage.setItem(
        `consultation_${consultationState.consultationId}`,
        JSON.stringify(consultationState)
      );
    } catch (error) {
      console.error("Error saving consultation state:", error);
    }
  };

  const handleBackPress = (): boolean => {
    if (consultationState.currentPhase > 1) {
      handlePreviousPhase();
      return true; // Prevent default back action
    }
    return false; // Allow default back action
  };

  const handleNextPhase = () => {
    if (consultationState.currentPhase < PHASES.length) {
      setConsultationState(prev => ({
        ...prev,
        currentPhase: prev.currentPhase + 1,
      }));
    }
  };

  const handlePreviousPhase = () => {
    if (consultationState.currentPhase > 1) {
      setConsultationState(prev => ({
        ...prev,
        currentPhase: prev.currentPhase - 1,
      }));
    } else {
      handleCancel();
    }
  };

  const handleCancel = () => {
    Alert.alert(
      "Cancelar Consulta",
      "¿Estás seguro de que quieres cancelar la consulta? Se perderá todo el progreso.",
      [
        { text: "Continuar", style: "cancel" },
        {
          text: "Cancelar Consulta",
          style: "destructive",
          onPress: () => {
            // Clear saved state
            AsyncStorage.removeItem(`consultation_${consultationState.consultationId}`);
            onCancel();
          },
        },
      ]
    );
  };

  // Phase 1: Client Selection and Consent
  const handleClientSelected = (client: any, consent: any) => {
    setConsultationState(prev => ({
      ...prev,
      client,
      consentData: consent,
    }));
    handleNextPhase();
  };

  // Phase 2: Hair Diagnosis
  const handleDiagnosisComplete = (diagnosis: any) => {
    setConsultationState(prev => ({
      ...prev,
      diagnosisData: diagnosis,
    }));
    // Move to chemical history extraction
    handleNextPhase();
  };

  const handleChemicalHistoryComplete = (history: any) => {
    setConsultationState(prev => ({
      ...prev,
      chemicalHistory: history,
    }));
    // Move to diagnosis validation
    handleNextPhase();
  };

  const handleDiagnosisValidated = (validatedDiagnosis: any, stylistNotes: string) => {
    setConsultationState(prev => ({
      ...prev,
      validatedDiagnosis: { ...validatedDiagnosis, stylistNotes },
    }));
    handleNextPhase();
  };

  // Phase 3: Desired Color Definition
  const handleColorDefinitionComplete = (colorData: any) => {
    setConsultationState(prev => ({
      ...prev,
      desiredColor: colorData,
    }));
    handleNextPhase();
  };

  // Phase 4: Expert Formulation
  const handleFormulationComplete = (formula: any) => {
    setConsultationState(prev => ({
      ...prev,
      formula,
    }));
    handleNextPhase();
  };

  // Phase 5: Service Documentation
  const handleDocumentationComplete = (documentation: any) => {
    setConsultationState(prev => ({
      ...prev,
      documentation,
    }));

    // Complete consultation
    completeConsultation();
  };

  const completeConsultation = async () => {
    const finalConsultationData = {
      ...consultationState,
      endTime: new Date().toISOString(),
      totalDuration: Date.now() - new Date(consultationState.startTime).getTime(),
      status: "completed",
    };

    try {
      // Save to client history
      await saveToClientHistory(finalConsultationData);
      
      // Clear temporary consultation state
      await AsyncStorage.removeItem(`consultation_${consultationState.consultationId}`);
      
      // Notify completion
      onComplete(finalConsultationData);
      
      Alert.alert(
        "Consulta Completada",
        "La consulta de color ha sido guardada exitosamente en el historial del cliente.",
        [
          {
            text: "Ver Historial",
            onPress: () => router.push("/components/ClientManagement"),
          },
          {
            text: "Nueva Consulta",
            onPress: () => {
              // Reset for new consultation
              setConsultationState({
                currentPhase: 1,
                client: null,
                consentData: null,
                diagnosisData: null,
                chemicalHistory: null,
                validatedDiagnosis: null,
                desiredColor: null,
                formula: null,
                documentation: null,
                startTime: new Date().toISOString(),
                consultationId: `consultation_${Date.now()}`,
              });
            },
          },
        ]
      );
    } catch (error) {
      console.error("Error completing consultation:", error);
      Alert.alert("Error", "No se pudo guardar la consulta completa.");
    }
  };

  const saveToClientHistory = async (consultationData: any) => {
    try {
      // Get existing client history
      const existingHistory = await AsyncStorage.getItem("client_consultations");
      const history = existingHistory ? JSON.parse(existingHistory) : [];
      
      // Add new consultation
      history.push(consultationData);
      
      // Save updated history
      await AsyncStorage.setItem("client_consultations", JSON.stringify(history));
    } catch (error) {
      console.error("Error saving to client history:", error);
      throw error;
    }
  };

  const renderPhaseIndicator = () => {
    return (
      <View className="bg-white p-4 border-b border-gray-200">
        <View className="flex-row items-center justify-between mb-3">
          <TouchableOpacity onPress={handlePreviousPhase}>
            <ArrowLeft size={24} color="#374151" />
          </TouchableOpacity>
          <Text className="text-lg font-semibold">
            Fase {consultationState.currentPhase} de {PHASES.length}
          </Text>
          <View className="flex-row items-center">
            <Clock size={16} color="#6B7280" />
            <Text className="text-sm text-gray-600 ml-1">
              {Math.round((Date.now() - new Date(consultationState.startTime).getTime()) / 60000)} min
            </Text>
          </View>
        </View>

        <View className="flex-row">
          {PHASES.map((phase, index) => {
            const isActive = consultationState.currentPhase === phase.id;
            const isCompleted = consultationState.currentPhase > phase.id;
            const IconComponent = phase.icon;

            return (
              <View key={phase.id} className="flex-1 items-center">
                <View className={`w-8 h-8 rounded-full items-center justify-center ${
                  isCompleted ? "bg-green-500" : isActive ? "bg-blue-500" : "bg-gray-300"
                }`}>
                  {isCompleted ? (
                    <CheckCircle size={16} color="white" />
                  ) : (
                    <IconComponent size={16} color="white" />
                  )}
                </View>
                <Text className={`text-xs mt-1 text-center ${
                  isActive ? "text-blue-600 font-medium" : "text-gray-600"
                }`}>
                  {phase.name}
                </Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  const renderCurrentPhase = () => {
    const { currentPhase, client, diagnosisData, chemicalHistory, validatedDiagnosis, desiredColor, formula } = consultationState;

    switch (currentPhase) {
      case 1:
        return (
          <ClientSelectionWithConsent
            onClientSelected={handleClientSelected}
            onBack={handleCancel}
            stylistName="Estilista Profesional"
          />
        );

      case 2:
        if (!diagnosisData) {
          return (
            <EnhancedHairDiagnosis
              onDiagnosisComplete={handleDiagnosisComplete}
              onBack={handlePreviousPhase}
              clientName={client?.name || "Cliente"}
            />
          );
        } else if (!chemicalHistory) {
          return (
            <ChemicalHistoryExtractor
              clientId={client?.id || ""}
              clientName={client?.name || "Cliente"}
              onHistoryComplete={handleChemicalHistoryComplete}
              onBack={handlePreviousPhase}
            />
          );
        } else {
          return (
            <DiagnosisValidation
              aiDiagnosis={diagnosisData}
              onValidationComplete={handleDiagnosisValidated}
              onBack={handlePreviousPhase}
              clientName={client?.name || "Cliente"}
              stylistName="Estilista Profesional"
            />
          );
        }

      case 3:
        return (
          <DesiredColorDefinition
            onColorDefinitionComplete={handleColorDefinitionComplete}
            onBack={handlePreviousPhase}
            clientName={client?.name || "Cliente"}
          />
        );

      case 4:
        return (
          <ExpertFormulation
            diagnosis={validatedDiagnosis}
            desiredColor={desiredColor}
            chemicalHistory={chemicalHistory}
            onFormulationComplete={handleFormulationComplete}
            onBack={handlePreviousPhase}
            clientName={client?.name || "Cliente"}
          />
        );

      case 5:
        return (
          <ServiceDocumentation
            originalFormula={formula}
            onDocumentationComplete={handleDocumentationComplete}
            onBack={handlePreviousPhase}
            clientName={client?.name || "Cliente"}
            stylistName="Estilista Profesional"
          />
        );

      default:
        return (
          <View className="flex-1 justify-center items-center">
            <Text className="text-lg text-gray-600">Fase no encontrada</Text>
          </View>
        );
    }
  };

  return (
    <View className="flex-1 bg-gray-50">
      {renderPhaseIndicator()}
      {renderCurrentPhase()}
    </View>
  );
};

export default EnhancedColorConsultation;
