import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Alert,
  Image,
  Modal,
} from "react-native";
import {
  Camera,
  Upload,
  CheckCircle,
  X,
  Eye,
  Zap,
  Shield,
  RefreshCw,
  AlertTriangle,
} from "lucide-react-native";
import * as ImagePicker from "expo-image-picker";
import * as FaceDetector from "expo-face-detector";
import * as ImageManipulator from "expo-image-manipulator";

interface HairZone {
  id: string;
  name: string;
  description: string;
  required: boolean;
  captured: boolean;
  imageUri?: string;
  quality?: "low" | "medium" | "high";
}

interface DiagnosisData {
  zones: {
    roots: ZoneAnalysis;
    midLengths: ZoneAnalysis;
    ends: ZoneAnalysis;
  };
  overall: {
    naturalLevel: number;
    undertone: string;
    porosity: string;
    condition: string;
    canasPercentage: number;
    diameter: string;
    density: string;
    elasticity: string;
    resistance: string;
    existingReflections: string[];
  };
  analysisConfidence: number;
  timestamp: string;
}

interface ZoneAnalysis {
  naturalLevel: number;
  undertone: string;
  condition: string;
  porosity: string;
  canasPercentage: number;
  notes: string;
}

interface EnhancedHairDiagnosisProps {
  onDiagnosisComplete: (diagnosis: DiagnosisData) => void;
  onBack?: () => void;
  clientName: string;
}

const HAIR_ZONES: HairZone[] = [
  {
    id: "frontal",
    name: "Frontal",
    description: "Vista frontal del cabello, incluyendo flequillo y línea de nacimiento",
    required: true,
    captured: false,
  },
  {
    id: "crown",
    name: "Coronilla",
    description: "Parte superior de la cabeza, zona de mayor exposición",
    required: true,
    captured: false,
  },
  {
    id: "back",
    name: "Nuca",
    description: "Parte posterior del cabello, zona de menor exposición",
    required: true,
    captured: false,
  },
  {
    id: "left_side",
    name: "Lateral Izquierdo",
    description: "Vista lateral izquierda para análisis de medios y puntas",
    required: false,
    captured: false,
  },
  {
    id: "right_side",
    name: "Lateral Derecho",
    description: "Vista lateral derecha para análisis completo",
    required: false,
    captured: false,
  },
];

const EnhancedHairDiagnosis: React.FC<EnhancedHairDiagnosisProps> = ({
  onDiagnosisComplete,
  onBack,
  clientName,
}) => {
  const [zones, setZones] = useState<HairZone[]>(HAIR_ZONES);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [showGuide, setShowGuide] = useState(false);
  const [selectedZone, setSelectedZone] = useState<HairZone | null>(null);
  const [analysisProgress, setAnalysisProgress] = useState(0);

  useEffect(() => {
    requestPermissions();
  }, []);

  const requestPermissions = async () => {
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== "granted") {
      Alert.alert(
        "Permisos Requeridos",
        "Necesitamos acceso a la cámara para tomar fotos del cabello."
      );
    }
  };

  const detectAndBlurFaces = async (imageUri: string): Promise<string> => {
    try {
      // Detect faces in the image
      const faces = await FaceDetector.detectFacesAsync(imageUri, {
        mode: FaceDetector.FaceDetectorMode.fast,
        detectLandmarks: FaceDetector.FaceDetectorLandmarks.none,
        runClassifications: FaceDetector.FaceDetectorClassifications.none,
      });

      if (faces.length === 0) {
        // No faces detected, return original image
        return imageUri;
      }

      // Apply blur to detected faces
      let manipulatedImage = imageUri;
      
      for (const face of faces) {
        const { bounds } = face;
        
        // Add padding to the face bounds for better coverage
        const padding = 20;
        const blurRegion = {
          originX: Math.max(0, bounds.origin.x - padding),
          originY: Math.max(0, bounds.origin.y - padding),
          width: bounds.size.width + (padding * 2),
          height: bounds.size.height + (padding * 2),
        };

        // Apply blur effect to the face region
        const result = await ImageManipulator.manipulateAsync(
          manipulatedImage,
          [
            {
              crop: blurRegion,
            },
          ],
          {
            compress: 0.8,
            format: ImageManipulator.SaveFormat.JPEG,
          }
        );

        // Note: This is a simplified implementation
        // In a real app, you'd need a more sophisticated blur effect
        manipulatedImage = result.uri;
      }

      return manipulatedImage;
    } catch (error) {
      console.error("Error detecting/blurring faces:", error);
      // Return original image if face detection fails
      return imageUri;
    }
  };

  const validateImageQuality = (imageUri: string): "low" | "medium" | "high" => {
    // This is a simplified quality check
    // In a real implementation, you'd analyze image properties like:
    // - Resolution
    // - Brightness
    // - Blur detection
    // - Focus quality
    
    // For now, we'll simulate quality assessment
    const randomQuality = Math.random();
    if (randomQuality > 0.7) return "high";
    if (randomQuality > 0.4) return "medium";
    return "low";
  };

  const captureImage = async (zone: HairZone) => {
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        
        // Apply face detection and blurring
        const processedImageUri = await detectAndBlurFaces(imageUri);
        
        // Validate image quality
        const quality = validateImageQuality(processedImageUri);
        
        if (quality === "low") {
          Alert.alert(
            "Calidad de Imagen Baja",
            "La imagen capturada tiene baja calidad. ¿Deseas tomarla de nuevo para obtener mejores resultados de análisis?",
            [
              { text: "Usar Esta Imagen", onPress: () => updateZoneImage(zone, processedImageUri, quality) },
              { text: "Tomar de Nuevo", onPress: () => captureImage(zone) },
            ]
          );
        } else {
          updateZoneImage(zone, processedImageUri, quality);
        }
      }
    } catch (error) {
      console.error("Error capturing image:", error);
      Alert.alert("Error", "No se pudo capturar la imagen. Inténtalo de nuevo.");
    }
  };

  const selectFromGallery = async (zone: HairZone) => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const imageUri = result.assets[0].uri;
        
        // Apply face detection and blurring
        const processedImageUri = await detectAndBlurFaces(imageUri);
        
        // Validate image quality
        const quality = validateImageQuality(processedImageUri);
        
        updateZoneImage(zone, processedImageUri, quality);
      }
    } catch (error) {
      console.error("Error selecting image:", error);
      Alert.alert("Error", "No se pudo seleccionar la imagen. Inténtalo de nuevo.");
    }
  };

  const updateZoneImage = (zone: HairZone, imageUri: string, quality: "low" | "medium" | "high") => {
    setZones(prevZones =>
      prevZones.map(z =>
        z.id === zone.id
          ? { ...z, captured: true, imageUri, quality }
          : z
      )
    );
  };

  const removeZoneImage = (zone: HairZone) => {
    setZones(prevZones =>
      prevZones.map(z =>
        z.id === zone.id
          ? { ...z, captured: false, imageUri: undefined, quality: undefined }
          : z
      )
    );
  };

  const getRequiredZonesCaptured = () => {
    return zones.filter(z => z.required && z.captured).length;
  };

  const getTotalRequiredZones = () => {
    return zones.filter(z => z.required).length;
  };

  const canProceedWithAnalysis = () => {
    return getRequiredZonesCaptured() >= 3; // Minimum 3 images required
  };

  const simulateAIAnalysis = async (): Promise<DiagnosisData> => {
    // Simulate AI analysis with realistic processing time
    const steps = [
      "Detectando características capilares...",
      "Analizando nivel natural por zonas...",
      "Evaluando porosidad y condición...",
      "Calculando porcentaje de canas...",
      "Identificando subtonos...",
      "Generando diagnóstico completo...",
    ];

    for (let i = 0; i < steps.length; i++) {
      setAnalysisProgress((i + 1) / steps.length * 100);
      await new Promise(resolve => setTimeout(resolve, 800));
    }

    // Generate realistic diagnosis data
    const mockDiagnosis: DiagnosisData = {
      zones: {
        roots: {
          naturalLevel: 4,
          undertone: "Cálido",
          condition: "Saludable",
          porosity: "Media",
          canasPercentage: 15,
          notes: "Raíces con crecimiento saludable, ligera presencia de canas",
        },
        midLengths: {
          naturalLevel: 5,
          undertone: "Cálido",
          condition: "Ligeramente dañado",
          porosity: "Media-Alta",
          canasPercentage: 5,
          notes: "Medios con ligero daño por procesos químicos anteriores",
        },
        ends: {
          naturalLevel: 6,
          undertone: "Neutro",
          condition: "Dañado",
          porosity: "Alta",
          canasPercentage: 0,
          notes: "Puntas dañadas, requieren tratamiento reconstructor",
        },
      },
      overall: {
        naturalLevel: 4.5,
        undertone: "Cálido",
        porosity: "Media-Alta",
        condition: "Moderadamente dañado",
        canasPercentage: 12,
        diameter: "Medio",
        density: "Media",
        elasticity: "Reducida",
        resistance: "Media",
        existingReflections: ["Dorados", "Cobrizos"],
      },
      analysisConfidence: 87,
      timestamp: new Date().toISOString(),
    };

    return mockDiagnosis;
  };

  const handleStartAnalysis = async () => {
    if (!canProceedWithAnalysis()) {
      Alert.alert(
        "Imágenes Insuficientes",
        "Se requieren al menos 3 imágenes de las zonas obligatorias para realizar un análisis preciso."
      );
      return;
    }

    setIsAnalyzing(true);
    setAnalysisProgress(0);

    try {
      const diagnosis = await simulateAIAnalysis();
      onDiagnosisComplete(diagnosis);
    } catch (error) {
      console.error("Error during analysis:", error);
      Alert.alert("Error", "No se pudo completar el análisis. Inténtalo de nuevo.");
    } finally {
      setIsAnalyzing(false);
      setAnalysisProgress(0);
    }
  };

  const showImageOptions = (zone: HairZone) => {
    Alert.alert(
      `Capturar ${zone.name}`,
      zone.description,
      [
        {
          text: "Tomar Foto",
          onPress: () => captureImage(zone),
        },
        {
          text: "Elegir de Galería",
          onPress: () => selectFromGallery(zone),
        },
        {
          text: "Cancelar",
          style: "cancel",
        },
      ]
    );
  };

  const renderZoneCard = (zone: HairZone) => {
    const getQualityColor = (quality?: string) => {
      switch (quality) {
        case "high": return "text-green-600";
        case "medium": return "text-yellow-600";
        case "low": return "text-red-600";
        default: return "text-gray-600";
      }
    };

    const getQualityText = (quality?: string) => {
      switch (quality) {
        case "high": return "Alta Calidad";
        case "medium": return "Calidad Media";
        case "low": return "Baja Calidad";
        default: return "";
      }
    };

    return (
      <View
        key={zone.id}
        className={`bg-white rounded-lg p-4 mb-3 border-2 ${
          zone.captured ? "border-green-200" : zone.required ? "border-blue-200" : "border-gray-200"
        }`}
      >
        <View className="flex-row items-center justify-between mb-2">
          <View className="flex-row items-center">
            <Text className="text-lg font-semibold">
              {zone.name}
              {zone.required && <Text className="text-red-500"> *</Text>}
            </Text>
            {zone.captured && (
              <CheckCircle size={20} color="#10B981" className="ml-2" />
            )}
          </View>
          {zone.quality && (
            <Text className={`text-sm font-medium ${getQualityColor(zone.quality)}`}>
              {getQualityText(zone.quality)}
            </Text>
          )}
        </View>

        <Text className="text-gray-600 text-sm mb-3">{zone.description}</Text>

        <View className="flex-row items-center justify-between">
          {zone.captured && zone.imageUri ? (
            <View className="flex-row items-center flex-1">
              <Image
                source={{ uri: zone.imageUri }}
                className="w-16 h-16 rounded-lg mr-3"
                resizeMode="cover"
              />
              <View className="flex-1">
                <TouchableOpacity
                  className="bg-blue-50 rounded-lg p-2 mb-2"
                  onPress={() => showImageOptions(zone)}
                >
                  <Text className="text-blue-700 text-center text-sm">
                    Reemplazar
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  className="bg-red-50 rounded-lg p-2"
                  onPress={() => removeZoneImage(zone)}
                >
                  <Text className="text-red-700 text-center text-sm">
                    Eliminar
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
          ) : (
            <TouchableOpacity
              className={`flex-1 py-3 px-4 rounded-lg flex-row items-center justify-center ${
                zone.required ? "bg-blue-500" : "bg-gray-500"
              }`}
              onPress={() => showImageOptions(zone)}
            >
              <Camera size={18} color="white" />
              <Text className="text-white font-medium ml-2">
                Capturar {zone.name}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  return (
    <View className="flex-1 bg-gray-50">
      <View className="p-4">
        <View className="flex-row items-center justify-between mb-4">
          {onBack && (
            <TouchableOpacity onPress={onBack}>
              <X size={24} color="#666" />
            </TouchableOpacity>
          )}
          <View className="flex-1">
            <Text className="text-2xl font-bold text-gray-800">
              Diagnóstico Capilar
            </Text>
            <Text className="text-gray-600">
              {clientName} - Análisis IA por zonas
            </Text>
          </View>
          <TouchableOpacity
            onPress={() => setShowGuide(true)}
            className="bg-blue-50 p-2 rounded-lg"
          >
            <Eye size={20} color="#3B82F6" />
          </TouchableOpacity>
        </View>

        {/* Progress Indicator */}
        <View className="bg-white rounded-lg p-4 mb-4">
          <View className="flex-row items-center justify-between mb-2">
            <Text className="font-semibold">Progreso de Captura</Text>
            <Text className="text-sm text-gray-600">
              {getRequiredZonesCaptured()}/{getTotalRequiredZones()} obligatorias
            </Text>
          </View>
          <View className="bg-gray-200 rounded-full h-2">
            <View
              className="bg-blue-500 rounded-full h-2"
              style={{
                width: `${(getRequiredZonesCaptured() / getTotalRequiredZones()) * 100}%`,
              }}
            />
          </View>
        </View>
      </View>

      <ScrollView className="flex-1 px-4">
        {zones.map(renderZoneCard)}
      </ScrollView>

      {/* Analysis Button */}
      <View className="p-4 bg-white border-t border-gray-200">
        <TouchableOpacity
          className={`py-3 px-4 rounded-lg flex-row items-center justify-center ${
            canProceedWithAnalysis() && !isAnalyzing
              ? "bg-green-500"
              : "bg-gray-300"
          }`}
          onPress={handleStartAnalysis}
          disabled={!canProceedWithAnalysis() || isAnalyzing}
        >
          {isAnalyzing ? (
            <>
              <RefreshCw size={18} color="white" className="animate-spin" />
              <Text className="text-white font-semibold ml-2">
                Analizando... {Math.round(analysisProgress)}%
              </Text>
            </>
          ) : (
            <>
              <Zap size={18} color="white" />
              <Text className="text-white font-semibold ml-2">
                {canProceedWithAnalysis()
                  ? `Analizar ${zones.filter(z => z.captured).length} Imágenes con IA`
                  : "Captura al menos 3 imágenes"}
              </Text>
            </>
          )}
        </TouchableOpacity>
      </View>

      {/* Guide Modal */}
      <Modal
        visible={showGuide}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <View className="flex-1 bg-white">
          <View className="flex-row items-center justify-between p-4 border-b border-gray-200">
            <TouchableOpacity onPress={() => setShowGuide(false)}>
              <X size={24} color="#666" />
            </TouchableOpacity>
            <Text className="text-lg font-semibold">Guía de Captura</Text>
            <View style={{ width: 24 }} />
          </View>

          <ScrollView className="flex-1 p-4">
            <View className="mb-6">
              <Text className="text-xl font-bold text-gray-800 mb-2">
                Consejos para Mejores Resultados
              </Text>
              <Text className="text-gray-600">
                Sigue estas recomendaciones para obtener el análisis más preciso:
              </Text>
            </View>

            <View className="space-y-4">
              <View className="bg-blue-50 p-4 rounded-lg">
                <Text className="font-semibold text-blue-900 mb-2">
                  💡 Iluminación Óptima
                </Text>
                <Text className="text-blue-800">
                  • Usa luz natural siempre que sea posible{"\n"}
                  • Evita sombras fuertes en el cabello{"\n"}
                  • No uses flash directo
                </Text>
              </View>

              <View className="bg-green-50 p-4 rounded-lg">
                <Text className="font-semibold text-green-900 mb-2">
                  📸 Técnica de Captura
                </Text>
                <Text className="text-green-800">
                  • Mantén el teléfono estable{"\n"}
                  • Enfoca bien antes de disparar{"\n"}
                  • Captura desde diferentes ángulos{"\n"}
                  • Asegúrate de que el cabello esté visible
                </Text>
              </View>

              <View className="bg-yellow-50 p-4 rounded-lg">
                <Text className="font-semibold text-yellow-900 mb-2">
                  🔒 Privacidad Garantizada
                </Text>
                <Text className="text-yellow-800">
                  • Los rostros se difuminan automáticamente{"\n"}
                  • Solo se analiza el cabello{"\n"}
                  • Las imágenes se procesan de forma segura
                </Text>
              </View>

              <View className="bg-purple-50 p-4 rounded-lg">
                <Text className="font-semibold text-purple-900 mb-2">
                  🎯 Zonas Requeridas
                </Text>
                <Text className="text-purple-800">
                  • Frontal: Línea de nacimiento y flequillo{"\n"}
                  • Coronilla: Parte superior de la cabeza{"\n"}
                  • Nuca: Parte posterior del cabello{"\n"}
                  • Laterales: Opcionales pero recomendadas
                </Text>
              </View>
            </View>

            <View className="mt-6 p-4 bg-gray-50 rounded-lg">
              <Text className="text-sm text-gray-600 text-center">
                El análisis IA evaluará cada zona por separado para crear
                una formulación más precisa y personalizada.
              </Text>
            </View>
          </ScrollView>

          <View className="p-4 border-t border-gray-200">
            <TouchableOpacity
              className="bg-blue-500 py-3 px-4 rounded-lg"
              onPress={() => setShowGuide(false)}
            >
              <Text className="text-white font-semibold text-center">
                Entendido, Comenzar Captura
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default EnhancedHairDiagnosis;
