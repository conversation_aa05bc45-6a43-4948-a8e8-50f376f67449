import AsyncStorage from "@react-native-async-storage/async-storage";

export interface Brand {
  id: string;
  name: string;
  productLines: ProductLine[];
}

export interface ProductLine {
  id: string;
  name: string;
  description: string;
  category?: string;
  colorRange?: string[];
  developer?: string[];
}

export interface UserSettings {
  profile: {
    name: string;
    email: string;
    phone: string;
  };
  salon: {
    name: string;
    address: string;
    country: string;
    language: string;
  };
  brands: {
    selectedBrands: string[];
    selectedProductLines: string[];
  };
  notifications: {
    appointments: boolean;
    reminders: boolean;
    marketing: boolean;
  };
  privacy: {
    dataSharing: boolean;
    analytics: boolean;
  };
}

// Complete brand database with detailed product information
export const AVAILABLE_BRANDS: Brand[] = [
  {
    id: "loreal",
    name: "L'Oréal Professionnel",
    productLines: [
      {
        id: "majirel",
        name: "<PERSON><PERSON><PERSON>",
        description: "Coloración permanente de alta cobertura",
        category: "permanent",
        colorRange: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"],
        developer: ["10vol", "20vol", "30vol", "40vol"],
      },
      {
        id: "inoa",
        name: "INOA",
        description: "Coloración sin amoníaco con tecnología ODS",
        category: "permanent",
        colorRange: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"],
        developer: ["10vol", "20vol", "30vol"],
      },
      {
        id: "dialight",
        name: "Dialight",
        description: "Tono sobre tono sin amoníaco",
        category: "semi-permanent",
        colorRange: ["4", "5", "6", "7", "8", "9", "10"],
        developer: ["6vol", "9vol"],
      },
      {
        id: "majiblond",
        name: "Majiblond",
        description: "Decoloración ultra compacta",
        category: "bleach",
        developer: ["10vol", "20vol", "30vol", "40vol"],
      },
    ],
  },
  {
    id: "wella",
    name: "Wella Professionals",
    productLines: [
      {
        id: "koleston",
        name: "Koleston Perfect",
        description: "Coloración permanente con tecnología ME+",
        category: "permanent",
        colorRange: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"],
        developer: ["12vol", "20vol", "30vol", "40vol"],
      },
      {
        id: "illumina",
        name: "Illumina Color",
        description: "Color con tecnología Microlight",
        category: "permanent",
        colorRange: ["3", "4", "5", "6", "7", "8", "9", "10"],
        developer: ["12vol", "20vol", "30vol"],
      },
      {
        id: "color-touch",
        name: "Color Touch",
        description: "Tono sobre tono intensivo",
        category: "semi-permanent",
        colorRange: ["2", "3", "4", "5", "6", "7", "8", "9", "10"],
        developer: ["4%", "1.9%"],
      },
      {
        id: "blondor",
        name: "Blondor",
        description: "Decoloración multi-blonde",
        category: "bleach",
        developer: ["12vol", "20vol", "30vol", "40vol"],
      },
    ],
  },
  {
    id: "schwarzkopf",
    name: "Schwarzkopf Professional",
    productLines: [
      {
        id: "igora-royal",
        name: "Igora Royal",
        description: "Coloración permanente de alta definición",
        category: "permanent",
        colorRange: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"],
        developer: ["10vol", "20vol", "30vol", "40vol"],
      },
      {
        id: "igora-vibrance",
        name: "Igora Vibrance",
        description: "Tono sobre tono sin amoníaco",
        category: "semi-permanent",
        colorRange: ["4", "5", "6", "7", "8", "9", "10"],
        developer: ["4%"],
      },
      {
        id: "blondme",
        name: "BlondMe",
        description: "Sistema de aclarado premium",
        category: "bleach",
        developer: ["10vol", "20vol", "30vol", "40vol"],
      },
    ],
  },
  {
    id: "matrix",
    name: "Matrix",
    productLines: [
      {
        id: "socolor",
        name: "SoColor",
        description: "Coloración permanente con Cera-Oil",
        category: "permanent",
        colorRange: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"],
        developer: ["10vol", "20vol", "30vol", "40vol"],
      },
      {
        id: "color-sync",
        name: "Color Sync",
        description: "Tono sobre tono sin amoníaco",
        category: "semi-permanent",
        colorRange: ["2", "3", "4", "5", "6", "7", "8", "9", "10"],
        developer: ["10vol", "20vol"],
      },
      {
        id: "light-master",
        name: "Light Master",
        description: "Decoloración de alta performance",
        category: "bleach",
        developer: ["10vol", "20vol", "30vol", "40vol"],
      },
    ],
  },
  {
    id: "redken",
    name: "Redken",
    productLines: [
      {
        id: "chromatics",
        name: "Chromatics",
        description: "Coloración permanente sin amoníaco",
        category: "permanent",
        colorRange: ["2", "3", "4", "5", "6", "7", "8", "9", "10"],
        developer: ["10vol", "20vol", "30vol"],
      },
      {
        id: "shades-eq",
        name: "Shades EQ",
        description: "Gloss acidificante",
        category: "semi-permanent",
        colorRange: ["2", "3", "4", "5", "6", "7", "8", "9", "10"],
        developer: ["Processing Solution"],
      },
      {
        id: "flashlift",
        name: "Flashlift",
        description: "Decoloración rápida",
        category: "bleach",
        developer: ["10vol", "20vol", "30vol", "40vol"],
      },
    ],
  },
];

class UserPreferencesService {
  private static instance: UserPreferencesService;
  private settings: UserSettings | null = null;

  private constructor() {}

  public static getInstance(): UserPreferencesService {
    if (!UserPreferencesService.instance) {
      UserPreferencesService.instance = new UserPreferencesService();
    }
    return UserPreferencesService.instance;
  }

  async loadSettings(): Promise<UserSettings> {
    try {
      const savedSettings = await AsyncStorage.getItem("salonier_settings");
      if (savedSettings) {
        this.settings = JSON.parse(savedSettings);
        return this.settings!;
      }
      
      // Return default settings if none exist
      const defaultSettings: UserSettings = {
        profile: {
          name: "Estilista",
          email: "",
          phone: "",
        },
        salon: {
          name: "Mi Salón",
          address: "",
          country: "España",
          language: "es",
        },
        brands: {
          selectedBrands: ["loreal", "wella"],
          selectedProductLines: ["majirel", "inoa", "koleston"],
        },
        notifications: {
          appointments: true,
          reminders: true,
          marketing: false,
        },
        privacy: {
          dataSharing: false,
          analytics: true,
        },
      };

      this.settings = defaultSettings;
      return defaultSettings;
    } catch (error) {
      console.error("Error loading settings:", error);
      throw new Error("No se pudieron cargar las preferencias del usuario");
    }
  }

  async getSelectedBrands(): Promise<Brand[]> {
    const settings = await this.loadSettings();
    return AVAILABLE_BRANDS.filter(brand => 
      settings.brands.selectedBrands.includes(brand.id)
    );
  }

  async getSelectedProductLines(): Promise<ProductLine[]> {
    const settings = await this.loadSettings();
    const selectedBrands = await this.getSelectedBrands();
    
    const allProductLines: ProductLine[] = [];
    selectedBrands.forEach(brand => {
      brand.productLines.forEach(line => {
        if (settings.brands.selectedProductLines.includes(line.id)) {
          allProductLines.push({
            ...line,
            // Add brand info to product line for context
            name: `${brand.name} ${line.name}`,
          });
        }
      });
    });

    return allProductLines;
  }

  async getBrandById(brandId: string): Promise<Brand | null> {
    return AVAILABLE_BRANDS.find(brand => brand.id === brandId) || null;
  }

  async getProductLineById(productLineId: string): Promise<ProductLine | null> {
    for (const brand of AVAILABLE_BRANDS) {
      const productLine = brand.productLines.find(line => line.id === productLineId);
      if (productLine) {
        return {
          ...productLine,
          name: `${brand.name} ${productLine.name}`,
        };
      }
    }
    return null;
  }

  async getFormulationContext(): Promise<{
    brands: Brand[];
    productLines: ProductLine[];
    stylistName: string;
    salonName: string;
  }> {
    const settings = await this.loadSettings();
    const brands = await this.getSelectedBrands();
    const productLines = await this.getSelectedProductLines();

    return {
      brands,
      productLines,
      stylistName: settings.profile.name,
      salonName: settings.salon.name,
    };
  }

  // Helper method to get products by category
  async getProductsByCategory(category: string): Promise<ProductLine[]> {
    const productLines = await this.getSelectedProductLines();
    return productLines.filter(line => line.category === category);
  }

  // Helper method to check if a specific brand is selected
  async isBrandSelected(brandId: string): Promise<boolean> {
    const settings = await this.loadSettings();
    return settings.brands.selectedBrands.includes(brandId);
  }

  // Helper method to check if a specific product line is selected
  async isProductLineSelected(productLineId: string): Promise<boolean> {
    const settings = await this.loadSettings();
    return settings.brands.selectedProductLines.includes(productLineId);
  }
}

export default UserPreferencesService;
